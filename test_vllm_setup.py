#!/usr/bin/env python3
"""
vLLM环境测试脚本
验证vLLM是否正确安装并可以正常工作
"""

import os
import sys

def test_vllm_import():
    """测试vLLM导入"""
    try:
        from vllm import LLM, SamplingParams
        from vllm.lora.request import LoRARequest
        print("✅ vLLM导入成功")
        return True
    except ImportError as e:
        print(f"❌ vLLM导入失败: {e}")
        return False

def test_transformers_import():
    """测试transformers导入"""
    try:
        from transformers import AutoTokenizer
        print("✅ transformers导入成功")
        return True
    except ImportError as e:
        print(f"❌ transformers导入失败: {e}")
        return False

def test_torch_cuda():
    """测试PyTorch CUDA支持"""
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ CUDA设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        return True
    except Exception as e:
        print(f"❌ PyTorch CUDA测试失败: {e}")
        return False

def test_model_paths():
    """测试模型路径是否存在"""
    base_model_path = "/opt/LLM_MODEL/Qwen2.5-7B-Instruct/qwen/Qwen2.5-7B-Instruct/"
    finetuned_model_path = "/opt/fuyu/test/output/qwen2.5-7B/checkpoint-1200"
    
    if os.path.exists(base_model_path):
        print(f"✅ 基础模型路径存在: {base_model_path}")
    else:
        print(f"❌ 基础模型路径不存在: {base_model_path}")
        return False
    
    if os.path.exists(finetuned_model_path):
        print(f"✅ 微调模型路径存在: {finetuned_model_path}")
    else:
        print(f"❌ 微调模型路径不存在: {finetuned_model_path}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 vLLM环境测试开始")
    print("=" * 50)
    
    tests = [
        ("vLLM导入测试", test_vllm_import),
        ("transformers导入测试", test_transformers_import),
        ("PyTorch CUDA测试", test_torch_cuda),
        ("模型路径测试", test_model_paths)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行vLLM对比脚本")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
