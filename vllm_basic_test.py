#!/usr/bin/env python3
"""
vLLM基础测试脚本
用于诊断vLLM配置问题
"""

import os
import torch

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "5"

def test_basic_vllm():
    """基础vLLM测试"""
    try:
        print("🔍 开始基础vLLM测试...")
        
        # 检查CUDA
        print(f"CUDA可用: {torch.cuda.is_available()}")
        print(f"当前设备: {torch.cuda.current_device()}")
        print(f"设备名称: {torch.cuda.get_device_name()}")
        
        # 导入vLLM
        from vllm import LLM, SamplingParams
        print("✅ vLLM导入成功")
        
        # 使用一个小模型进行测试
        model_path = "/opt/LLM_MODEL/Qwen2.5-7B-Instruct/qwen/Qwen2.5-7B-Instruct/"
        
        print("📥 尝试加载模型...")
        
        # 最简配置
        llm = LLM(
            model=model_path,
            tensor_parallel_size=1,
            trust_remote_code=True,
            gpu_memory_utilization=0.5,
            max_model_len=1024,
            disable_log_stats=True
        )
        
        print("✅ 模型加载成功！")
        
        # 简单测试
        sampling_params = SamplingParams(temperature=0.0, max_tokens=50)
        
        prompts = ["Hello, how are you?"]
        outputs = llm.generate(prompts, sampling_params)
        
        for output in outputs:
            prompt = output.prompt
            generated_text = output.outputs[0].text
            print(f"输入: {prompt}")
            print(f"输出: {generated_text}")
        
        print("✅ vLLM基础测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ vLLM测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_config():
    """测试替代配置"""
    try:
        print("\n🔍 测试替代配置...")
        
        from vllm import LLM, SamplingParams
        
        model_path = "/opt/LLM_MODEL/Qwen2.5-7B-Instruct/qwen/Qwen2.5-7B-Instruct/"
        
        # 尝试不同的配置
        configs = [
            {
                "name": "配置1: 最小内存",
                "config": {
                    "model": model_path,
                    "tensor_parallel_size": 1,
                    "trust_remote_code": True,
                    "gpu_memory_utilization": 0.3,
                    "max_model_len": 512
                }
            },
            {
                "name": "配置2: 无并行",
                "config": {
                    "model": model_path,
                    "trust_remote_code": True,
                    "gpu_memory_utilization": 0.4,
                    "max_model_len": 1024
                }
            }
        ]
        
        for config_info in configs:
            try:
                print(f"\n尝试 {config_info['name']}...")
                llm = LLM(**config_info['config'])
                print(f"✅ {config_info['name']} 成功！")
                
                # 简单测试
                sampling_params = SamplingParams(temperature=0.0, max_tokens=20)
                outputs = llm.generate(["测试"], sampling_params)
                print(f"测试输出: {outputs[0].outputs[0].text}")
                
                return True
                
            except Exception as e:
                print(f"❌ {config_info['name']} 失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ 替代配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 vLLM基础诊断测试")
    print("=" * 50)
    
    # 基础测试
    if test_basic_vllm():
        print("\n🎉 基础测试通过！")
    else:
        print("\n⚠️ 基础测试失败，尝试替代配置...")
        if test_alternative_config():
            print("\n🎉 替代配置成功！")
        else:
            print("\n❌ 所有配置都失败了")

if __name__ == "__main__":
    main()
