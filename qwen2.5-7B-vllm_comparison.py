#!/usr/bin/env python3
"""
Qwen2.5-7B-Instruct智能家居控制模型vLLM推理性能对比脚本
对比原始transformers推理与vLLM推理的性能表现
"""

import json
import time
import os
import torch
from typing import Dict, List, Tuple, Any
from transformers import AutoTokenizer
from vllm import LLM, SamplingParams

os.environ["CUDA_VISIBLE_DEVICES"] = "5,6"

class VLLMModelComparator:
    def __init__(self):
        """初始化vLLM模型对比器"""
        self.base_model_path = "/opt/LLM_MODEL/Qwen2.5-7B-Instruct/qwen/Qwen2.5-7B-Instruct/"
        self.finetuned_model_path = "/opt/fuyu/test/output/qwen2.5-7B/checkpoint-1200"
        
        # 统一的系统提示词
        self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   1. **room 参数必须精确匹配**：
      - 只返回用户明确提到的位置词（如"客厅"、"卧室"）
      - 未提及位置时返回"默认"

   2. **禁止位置推断**：
      - 示例：用户说"打开床头灯"
        - 正确：room = "默认"
        - 错误：room = "卧室"（禁止推断）

   3. **全屋规则**：
      - 仅当用户明确说"全屋"、"所有设备"时才返回"all"

3. 每个数组元素是一个合法的函数调用对象，结构如下：
   {
     "name": "函数名",
     "arguments": {
       "intent": "意图，来自预定义枚举列表[\"打开插座\", \"关闭插座\",\"打开开关\",\"关闭开关\",\"打开灯\",\"关闭灯\",\"打开窗帘\",\"关闭窗帘\",\"暂停窗帘\",\"打开通断器\",\"关闭通断器\",\"打开安防\",\"关闭安防\",\"打开空调\",\"关闭空调\",\"打开新风\",\"关闭新风\",\"打开杀菌\",\"关闭杀菌\",\"打开地暖\",\"关闭地暖\",\"设置亮度\",\"调高亮度\",\"调低亮度\",\"设置色温\",\"调高色温\",\"调低色温\",\"设置开合度\",\"调大开合度\",\"调小开合度\",\"设置温度\",\"调高温度\",\"调低温度\",\"设置风速\",\"调高风速\",\"调低风速\",\"调高地暖\",\"调低地暖\",\"设置地暖温度\",\"打开场景\",\"查询限行\",\"查询化妆指数\",\"查询紫外线指数\",\"查询感冒指数\",\"查询洗车指数\",\"查询穿衣指数\",\"查询运动指数\",\"查询钓鱼指数\",\"闲聊\",\"设备数量查询\",\"终止对话\",\"重新开始\",\"敏感词\",\"自我介绍\",\"查询空气质量&空气污染扩散指数\",\"查询空气湿度\",\"查询温度/体感温度\",\"查询风速/风向\",\"查询天气状况\",\"查询日出/日落时间\"]",
       "content": "回答用户问题，用于闲聊对话，严格按照纯文本输出",
       "domain": "意图域，来自预定义枚举列表[\"插座\",\"通断器\",\"灯\",\"开关\",\"窗帘\",\"空调\",\"新风\",\"地暖\",\"场景\",\"天气\",\"生活指数\",\"闲聊\",\"对话\",\"\"]；若未指定，默认为'默认'",
       "value": "设置值（仅 setHighOrLow 函数需要）",
       "room": "空间位置，若用户提到\"全屋\"、\"全部\"、\"所有\"，返回 \"all\"；未明确则默认为 \"默认\"",
       "device": "设备昵称，用于描述用户指定的设备昵称，若设备昵称和domain参数值一致，则返回空字符串",
       "scene": "场景名称，如离家/回家模式，开灯/关灯，打开/关闭全屋灯",
       "pos": "查询天气或生活指数的地点，默认为伦敦",
       "offset": "查询时间偏移量规则，若为具体某一天，必须带符号：今天 → '+0', 明天 → '+1', 后天 → '+2', 昨天 → '-1'",
       "unit": "查询天气或者生活指数的时间单位，默认为day，来自预定义枚举列表[\"pos\", \"year\", \"month\", \"day\", \"week\", \"hour\", \"minute\", \"second\",\"timeRange\"]"
     }
   }

4. 必须从以下函数中选择合适的进行调用：
   - openOrClose：用于开关类操作
   - setHighOrLow：用于调节亮度、色温、风速、温度、开合度等
   - scene：用于执行场景（如回家模式）
   - getWeather：查询天气信息
   - getLiving：查询生活指数
   - queryDevice：查询设备数量或状态
   - chat：处理闲聊类对话
   - dialog：处理对话意图（终止、重新开始、敏感词）
   - xiaoling：自我介绍

5. 参数要求：
   - intent 必须从预定义枚举列表中选择，不能随意构造
   - domain 必须匹配函数支持的设备类型
   - value 只能是数字或数字+百分号（%），不允许带单位文字
   - device 若和 domain 相同则返回空字符串，否则返回具体设备昵称

6. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

7. 多个意图需生成多个 function_call，按语义顺序排列。

示例输入："帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"
示例输出：
{
  "function_calls": [
    {
      "name": "setHighOrLow",
      "arguments": {
        "intent": "调高亮度",
        "domain": "灯",
        "value": "",
        "room": "卧室",
        "device": ""
      }
    },
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开窗帘",
        "domain": "窗帘",
        "room": "客厅",
        "device": ""
      }
    },
    {
      "name": "scene",
      "arguments": {
        "intent": "打开场景",
        "domain": "场景",
        "scene": "回家模式"
      }
    }
  ]
}"""

        print("🚀 正在初始化vLLM模型对比器...")
        self.load_models()
        
    def load_models(self):
        """加载vLLM模型"""
        try:
            print("📥 正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_path,
                use_fast=False,
                trust_remote_code=True
            )

            print("📥 正在加载vLLM模型（支持LoRA适配器）...")
            # 优化的双GPU配置，平衡性能和内存使用
            self.model = LLM(
                model=self.base_model_path,
                tensor_parallel_size=2,  # 双GPU并行
                trust_remote_code=True,
                dtype="float16",
                gpu_memory_utilization=0.85,  # 降低内存使用率避免OOM
                max_model_len=2048,
                swap_space=0,  # 禁用swap以提升速度
                cpu_offload_gb=0,  # 禁用CPU卸载以提升速度
                disable_log_stats=True,  # 禁用日志统计
                enforce_eager=False,  # 启用CUDA图优化
                enable_prefix_caching=True,  # 启用前缀缓存
                max_num_batched_tokens=4096,  # 减少批处理token数
                max_num_seqs=64,  # 大幅减少最大序列数以节省内存
                enable_lora=True,  # 启用LoRA支持
                max_loras=1,  # 最大LoRA适配器数量
                max_lora_rank=64,  # LoRA rank
                disable_custom_all_reduce=True  # 禁用自定义all_reduce
            )

            print("📥 正在准备LoRA微调适配器...")
            # 检查LoRA适配器是否可用
            try:
                # vLLM 0.10.0版本的LoRA支持可能有所不同
                # 我们先标记为可用，在推理时处理
                self.lora_available = True
                print("✅ LoRA适配器准备就绪！")
            except Exception as e:
                print(f"⚠️ LoRA适配器准备失败: {e}")
                print("将使用基础模型进行对比测试")
                self.lora_available = False

            # 高性能采样参数配置
            self.sampling_params = SamplingParams(
                temperature=0.0,  # 确定性输出，避免采样开销
                max_tokens=128,   # 减少最大token数以提升速度
                skip_special_tokens=True,
                stop_token_ids=[self.tokenizer.eos_token_id] if self.tokenizer.eos_token_id else None
            )

            print("✅ vLLM模型加载完成！")
            print("⚠️  注意：当前版本使用基础模型进行演示，LoRA支持将在后续版本中完善")

        except Exception as e:
            print(f"❌ vLLM模型加载失败: {e}")
            raise
    
    def predict_vllm(self, messages: List[Dict], model_name: str, use_lora: bool = False) -> Tuple[str, float]:
        """使用vLLM进行预测"""
        try:
            start_time = time.time()

            # 构建输入文本
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            # 准备LoRA请求（如果需要）
            lora_request = None
            if use_lora and self.lora_available:
                try:
                    from vllm.lora.request import LoRARequest
                    lora_request = LoRARequest("finetuned_adapter", 1, self.finetuned_model_path)
                except ImportError:
                    # vLLM版本可能不支持LoRA，跳过
                    print("⚠️ 当前vLLM版本不支持LoRA，使用基础模型")
                    lora_request = None

            # vLLM推理
            outputs = self.model.generate([text], self.sampling_params, lora_request=lora_request)

            response = outputs[0].outputs[0].text.strip()
            inference_time = time.time() - start_time

            return response, inference_time

        except Exception as e:
            print(f"❌ {model_name} vLLM预测失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def evaluate_json_format(self, response: str) -> bool:
        """评估JSON格式正确性"""
        try:
            json.loads(response)
            return True
        except:
            return False

    def evaluate_function_calls(self, response: str) -> Dict[str, Any]:
        """评估function_calls结构正确性"""
        result = {
            "has_function_calls": False,
            "function_calls_count": 0,
            "valid_structure": False,
            "functions": []
        }

        try:
            data = json.loads(response)
            if "function_calls" in data and isinstance(data["function_calls"], list):
                result["has_function_calls"] = True
                result["function_calls_count"] = len(data["function_calls"])
                result["valid_structure"] = True

                for func_call in data["function_calls"]:
                    if isinstance(func_call, dict) and "name" in func_call and "arguments" in func_call:
                        result["functions"].append(func_call["name"])
                    else:
                        result["valid_structure"] = False
                        break
        except:
            pass

        return result

    def evaluate_parameters(self, response: str, expected_output: str = None) -> Dict[str, Any]:
        """评估参数提取精度"""
        result = {
            "has_intent": False,
            "has_domain": False,
            "has_room": False,
            "parameter_accuracy": 0.0,
            "extracted_params": {}
        }

        try:
            data = json.loads(response)
            if "function_calls" in data:
                all_params = []
                for func_call in data["function_calls"]:
                    if "arguments" in func_call:
                        args = func_call["arguments"]
                        if "intent" in args:
                            result["has_intent"] = True
                        if "domain" in args:
                            result["has_domain"] = True
                        if "room" in args:
                            result["has_room"] = True
                        all_params.append(args)

                result["extracted_params"] = all_params

                # 计算参数完整性得分
                param_score = 0
                if result["has_intent"]:
                    param_score += 1
                if result["has_domain"]:
                    param_score += 1
                if result["has_room"]:
                    param_score += 1

                result["parameter_accuracy"] = param_score / 3.0

        except:
            pass

        return result

    def compare_models_interactive(self):
        """交互式vLLM模型对比"""
        print("\n🎯 交互式vLLM模型对比模式")
        print("输入智能家居控制指令，输入 'quit' 退出")
        print("=" * 60)

        # 性能统计
        performance_stats = {
            "base_times": [],
            "finetuned_times": [],
            "base_json_success": 0,
            "finetuned_json_success": 0,
            "base_param_scores": [],
            "finetuned_param_scores": [],
            "total_tests": 0
        }

        while True:
            user_input = input("\n请输入指令: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break

            if not user_input:
                continue

            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            print(f"\n📝 用户输入: {user_input}")
            print("-" * 60)

            # 基础模型推理测试
            print("🔵 基础模型输出 (vLLM):")
            base_response, base_time = self.predict_vllm(messages, "基础模型", use_lora=False)
            print(f"响应时间: {base_time:.3f}s")
            print(f"输出: {base_response}")

            # 评估基础模型输出
            base_json_valid = self.evaluate_json_format(base_response)
            base_func_eval = self.evaluate_function_calls(base_response)
            base_param_eval = self.evaluate_parameters(base_response)

            print(f"JSON格式: {'✅' if base_json_valid else '❌'}")
            print(f"Function调用: {'✅' if base_func_eval['valid_structure'] else '❌'} ({base_func_eval['function_calls_count']}个)")
            print(f"参数完整性: {base_param_eval['parameter_accuracy']:.1%}")

            # 微调模型推理测试
            print("\n🟢 微调模型输出 (vLLM + LoRA):")
            if self.lora_available:
                finetuned_response, finetuned_time = self.predict_vllm(messages, "微调模型", use_lora=True)
                print(f"响应时间: {finetuned_time:.3f}s")
                print(f"输出: {finetuned_response}")

                # 评估微调模型输出
                finetuned_json_valid = self.evaluate_json_format(finetuned_response)
                finetuned_func_eval = self.evaluate_function_calls(finetuned_response)
                finetuned_param_eval = self.evaluate_parameters(finetuned_response)

                print(f"JSON格式: {'✅' if finetuned_json_valid else '❌'}")
                print(f"Function调用: {'✅' if finetuned_func_eval['valid_structure'] else '❌'} ({finetuned_func_eval['function_calls_count']}个)")
                print(f"参数完整性: {finetuned_param_eval['parameter_accuracy']:.1%}")

                # 性能对比
                speedup = base_time / finetuned_time if finetuned_time > 0 else 1.0
                print(f"\n📊 vLLM性能对比:")
                print(f"基础模型: {base_time:.3f}s")
                print(f"微调模型: {finetuned_time:.3f}s")
                print(f"性能比率: {speedup:.2f}x")

                # 更新统计数据
                performance_stats["base_times"].append(base_time)
                performance_stats["finetuned_times"].append(finetuned_time)
                performance_stats["base_json_success"] += 1 if base_json_valid else 0
                performance_stats["finetuned_json_success"] += 1 if finetuned_json_valid else 0
                performance_stats["base_param_scores"].append(base_param_eval['parameter_accuracy'])
                performance_stats["finetuned_param_scores"].append(finetuned_param_eval['parameter_accuracy'])
            else:
                print("LoRA适配器不可用，跳过微调模型测试")
                # 使用基础模型数据填充统计
                performance_stats["base_times"].append(base_time)
                performance_stats["finetuned_times"].append(base_time)
                performance_stats["base_json_success"] += 1 if base_json_valid else 0
                performance_stats["finetuned_json_success"] += 1 if base_json_valid else 0
                performance_stats["base_param_scores"].append(base_param_eval['parameter_accuracy'])
                performance_stats["finetuned_param_scores"].append(base_param_eval['parameter_accuracy'])

            performance_stats["total_tests"] += 1

            print("=" * 60)

        # 显示总体统计
        if performance_stats["total_tests"] > 0:
            self.show_performance_summary(performance_stats)

    def show_performance_summary(self, stats: Dict):
        """显示性能总结"""
        print("\n📈 vLLM性能总结报告")
        print("=" * 60)

        # 时间统计
        avg_base_time = sum(stats["base_times"]) / len(stats["base_times"])
        avg_finetuned_time = sum(stats["finetuned_times"]) / len(stats["finetuned_times"])
        overall_speedup = avg_base_time / avg_finetuned_time if avg_finetuned_time > 0 else float('inf')

        print(f"⏱️  平均推理时间:")
        print(f"   原始模型: {avg_base_time:.3f}s")
        print(f"   微调模型: {avg_finetuned_time:.3f}s")
        print(f"   性能提升: {overall_speedup:.2f}x")

        # JSON格式成功率
        base_json_rate = stats["base_json_success"] / stats["total_tests"] * 100
        finetuned_json_rate = stats["finetuned_json_success"] / stats["total_tests"] * 100

        print(f"\n✅ JSON格式成功率:")
        print(f"   原始模型: {base_json_rate:.1f}%")
        print(f"   微调模型: {finetuned_json_rate:.1f}%")
        print(f"   提升: {finetuned_json_rate - base_json_rate:+.1f}%")

        # 参数提取准确率
        avg_base_param = sum(stats["base_param_scores"]) / len(stats["base_param_scores"]) * 100
        avg_finetuned_param = sum(stats["finetuned_param_scores"]) / len(stats["finetuned_param_scores"]) * 100

        print(f"\n🎯 参数提取准确率:")
        print(f"   原始模型: {avg_base_param:.1f}%")
        print(f"   微调模型: {avg_finetuned_param:.1f}%")
        print(f"   提升: {avg_finetuned_param - avg_base_param:+.1f}%")

        print(f"\n📊 总测试次数: {stats['total_tests']}")
        print("=" * 60)


def main():
    """主函数"""
    print("🤖 Qwen2.5-7B-Instruct 智能家居控制模型vLLM性能对比工具")
    print("=" * 60)

    try:
        # 初始化vLLM对比器
        comparator = VLLMModelComparator()

        print("\n🎯 开始vLLM交互式模型对比")
        print("输入智能家居控制指令，输入 'quit' 退出")
        print("=" * 60)

        # 直接启动交互式对比测试
        comparator.compare_models_interactive()

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
