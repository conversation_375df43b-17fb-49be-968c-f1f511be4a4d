#!/usr/bin/env python3
"""
Qwen2.5-7B-Instruct智能家居控制模型性能对比脚本
对比不同推理配置的性能表现（基于transformers）
"""

import json
import time
import os
import torch
from typing import Dict, List, Tuple, Any
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

os.environ["CUDA_VISIBLE_DEVICES"] = "5,6"

# 修复transformers库的ALL_PARALLEL_STYLES问题
from transformers import modeling_utils
if not hasattr(modeling_utils, "ALL_PARALLEL_STYLES") or modeling_utils.ALL_PARALLEL_STYLES is None:
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]

class PerformanceComparator:
    def __init__(self):
        """初始化性能对比器"""
        self.base_model_path = "/opt/LLM_MODEL/Qwen2.5-7B-Instruct/qwen/Qwen2.5-7B-Instruct/"
        self.finetuned_model_path = "/opt/fuyu/test/output/qwen2.5-7B/checkpoint-1200"
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 统一的系统提示词
        self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   - room 参数必须精确匹配用户明确提到的位置词
   - 未提及位置时返回"默认"
   - 禁止位置推断

3. 每个数组元素是一个合法的函数调用对象，包含name和arguments字段。

4. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

示例输入："打开客厅的灯"
示例输出：
{
  "function_calls": [
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开灯",
        "domain": "灯",
        "room": "客厅",
        "device": ""
      }
    }
  ]
}"""

        print("🚀 正在初始化性能对比器...")
        self.load_models()
        
    def load_models(self):
        """加载原始模型和微调模型"""
        try:
            print("📥 正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_path, 
                use_fast=False, 
                trust_remote_code=True
            )
            
            print("📥 正在加载原始基础模型...")
            self.base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.float16,
                trust_remote_code=True,
                device_map="auto"
            )
            
            print("📥 正在加载微调模型...")
            base_for_finetuned = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.float16,
                trust_remote_code=True,
                device_map="auto"
            )
            self.finetuned_model = PeftModel.from_pretrained(
                base_for_finetuned, 
                self.finetuned_model_path
            )
             
            print("✅ 模型加载完成！")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def predict_fast(self, model, messages: List[Dict], model_name: str) -> Tuple[str, float]:
        """快速推理配置"""
        try:
            start_time = time.time()
            
            text = self.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            model_inputs = self.tokenizer([text], return_tensors="pt", padding=True, truncation=True)
            
            # 获取模型设备
            first_param_device = next(model.parameters()).device
            for k in model_inputs:
                model_inputs[k] = model_inputs[k].to(first_param_device)
            
            # 快速推理配置
            with torch.no_grad():
                generated_ids = model.generate(
                    input_ids=model_inputs.input_ids,
                    attention_mask=model_inputs.attention_mask,
                    max_new_tokens=128,  # 减少token数量
                    do_sample=False,     # 确定性输出
                    pad_token_id=self.tokenizer.pad_token_id or self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    use_cache=True       # 启用KV缓存
                )
            
            generated_ids = [
                output_ids[len(input_ids):] 
                for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            return response.strip(), time.time() - start_time
            
        except Exception as e:
            print(f"❌ {model_name}快速推理失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def predict_quality(self, model, messages: List[Dict], model_name: str) -> Tuple[str, float]:
        """质量优先推理配置"""
        try:
            start_time = time.time()
            
            text = self.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            model_inputs = self.tokenizer([text], return_tensors="pt", padding=True, truncation=True)
            
            first_param_device = next(model.parameters()).device
            for k in model_inputs:
                model_inputs[k] = model_inputs[k].to(first_param_device)
            
            # 质量优先配置
            with torch.no_grad():
                generated_ids = model.generate(
                    input_ids=model_inputs.input_ids,
                    attention_mask=model_inputs.attention_mask,
                    max_new_tokens=256,  # 更多token
                    do_sample=True,      # 启用采样
                    temperature=0.1,     # 低温度保持质量
                    top_p=0.9,          # nucleus采样
                    pad_token_id=self.tokenizer.pad_token_id or self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    use_cache=True
                )
            
            generated_ids = [
                output_ids[len(input_ids):] 
                for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            return response.strip(), time.time() - start_time
            
        except Exception as e:
            print(f"❌ {model_name}质量推理失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def evaluate_json_format(self, response: str) -> bool:
        """评估JSON格式正确性"""
        try:
            json.loads(response)
            return True
        except:
            return False

    def evaluate_function_calls(self, response: str) -> Dict[str, Any]:
        """评估function_calls结构正确性"""
        result = {
            "has_function_calls": False,
            "function_calls_count": 0,
            "valid_structure": False,
            "functions": []
        }

        try:
            data = json.loads(response)
            if "function_calls" in data and isinstance(data["function_calls"], list):
                result["has_function_calls"] = True
                result["function_calls_count"] = len(data["function_calls"])
                result["valid_structure"] = True

                for func_call in data["function_calls"]:
                    if isinstance(func_call, dict) and "name" in func_call and "arguments" in func_call:
                        result["functions"].append(func_call["name"])
                    else:
                        result["valid_structure"] = False
                        break
        except:
            pass

        return result

    def performance_benchmark(self):
        """性能基准测试"""
        print("\n🎯 性能基准测试模式")
        print("=" * 60)

        # 测试用例
        test_cases = [
            "打开客厅的灯",
            "关闭卧室的空调",
            "把客厅温度调到26度",
            "打开全屋的灯",
            "启动回家模式"
        ]

        # 测试配置
        test_configs = [
            ("快速推理", self.predict_fast),
            ("质量推理", self.predict_quality)
        ]

        results = {}

        for config_name, predict_func in test_configs:
            print(f"\n🔍 测试配置: {config_name}")
            print("-" * 40)
            
            config_results = {
                "base_times": [],
                "finetuned_times": [],
                "base_json_success": 0,
                "finetuned_json_success": 0,
                "total_tests": 0
            }

            for i, user_input in enumerate(test_cases, 1):
                print(f"\n测试 {i}/{len(test_cases)}: {user_input}")
                
                messages = [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ]

                # 原始模型测试
                base_response, base_time = predict_func(self.base_model, messages, "原始模型")
                base_json_valid = self.evaluate_json_format(base_response)
                
                # 微调模型测试
                finetuned_response, finetuned_time = predict_func(self.finetuned_model, messages, "微调模型")
                finetuned_json_valid = self.evaluate_json_format(finetuned_response)
                
                print(f"  原始模型: {base_time:.3f}s {'✅' if base_json_valid else '❌'}")
                print(f"  微调模型: {finetuned_time:.3f}s {'✅' if finetuned_json_valid else '❌'}")
                
                # 更新统计
                config_results["base_times"].append(base_time)
                config_results["finetuned_times"].append(finetuned_time)
                config_results["base_json_success"] += 1 if base_json_valid else 0
                config_results["finetuned_json_success"] += 1 if finetuned_json_valid else 0
                config_results["total_tests"] += 1

            results[config_name] = config_results

        # 显示总结
        self.show_benchmark_summary(results)

    def show_benchmark_summary(self, results: Dict):
        """显示基准测试总结"""
        print("\n📈 性能基准测试总结报告")
        print("=" * 60)
        
        for config_name, stats in results.items():
            print(f"\n🔧 {config_name} 配置:")
            print("-" * 30)
            
            # 时间统计
            avg_base_time = sum(stats["base_times"]) / len(stats["base_times"])
            avg_finetuned_time = sum(stats["finetuned_times"]) / len(stats["finetuned_times"])
            speedup = avg_base_time / avg_finetuned_time if avg_finetuned_time > 0 else 1.0
            
            print(f"⏱️  平均推理时间:")
            print(f"   原始模型: {avg_base_time:.3f}s")
            print(f"   微调模型: {avg_finetuned_time:.3f}s")
            print(f"   性能比率: {speedup:.2f}x")
            
            # JSON成功率
            base_json_rate = stats["base_json_success"] / stats["total_tests"] * 100
            finetuned_json_rate = stats["finetuned_json_success"] / stats["total_tests"] * 100
            
            print(f"✅ JSON格式成功率:")
            print(f"   原始模型: {base_json_rate:.1f}%")
            print(f"   微调模型: {finetuned_json_rate:.1f}%")
            print(f"   提升: {finetuned_json_rate - base_json_rate:+.1f}%")
        
        print("\n" + "=" * 60)


def main():
    """主函数"""
    print("🤖 Qwen2.5-7B-Instruct 智能家居控制模型性能对比工具")
    print("=" * 60)

    try:
        # 初始化对比器
        comparator = PerformanceComparator()

        print("\n🎯 开始性能基准测试")
        print("=" * 60)

        # 运行基准测试
        comparator.performance_benchmark()

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
