{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 12.727272727272727, "eval_steps": 500, "global_step": 1400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09090909090909091, "grad_norm": 1.6776601076126099, "learning_rate": 1.8e-05, "loss": 3.6298, "step": 10}, {"epoch": 0.18181818181818182, "grad_norm": 1.3293747901916504, "learning_rate": 3.8e-05, "loss": 3.3776, "step": 20}, {"epoch": 0.2727272727272727, "grad_norm": 1.2642600536346436, "learning_rate": 5.8e-05, "loss": 2.5695, "step": 30}, {"epoch": 0.36363636363636365, "grad_norm": 1.3439741134643555, "learning_rate": 7.800000000000001e-05, "loss": 1.5407, "step": 40}, {"epoch": 0.45454545454545453, "grad_norm": 0.5044201612472534, "learning_rate": 9.8e-05, "loss": 0.6095, "step": 50}, {"epoch": 0.5454545454545454, "grad_norm": 0.49930620193481445, "learning_rate": 0.000118, "loss": 0.3431, "step": 60}, {"epoch": 0.6363636363636364, "grad_norm": 0.29429003596305847, "learning_rate": 0.000138, "loss": 0.2534, "step": 70}, {"epoch": 0.7272727272727273, "grad_norm": 0.3159531056880951, "learning_rate": 0.00015800000000000002, "loss": 0.2072, "step": 80}, {"epoch": 0.8181818181818182, "grad_norm": 0.2737470865249634, "learning_rate": 0.00017800000000000002, "loss": 0.1585, "step": 90}, {"epoch": 0.9090909090909091, "grad_norm": 0.19942419230937958, "learning_rate": 0.00019800000000000002, "loss": 0.1381, "step": 100}, {"epoch": 1.0, "grad_norm": 0.22793202102184296, "learning_rate": 0.00019943750000000002, "loss": 0.135, "step": 110}, {"epoch": 1.0909090909090908, "grad_norm": 0.1663619577884674, "learning_rate": 0.00019881250000000001, "loss": 0.1264, "step": 120}, {"epoch": 1.1818181818181819, "grad_norm": 0.11978945881128311, "learning_rate": 0.0001981875, "loss": 0.1196, "step": 130}, {"epoch": 1.2727272727272727, "grad_norm": 0.14593049883842468, "learning_rate": 0.0001975625, "loss": 0.1207, "step": 140}, {"epoch": 1.3636363636363638, "grad_norm": 0.18781854212284088, "learning_rate": 0.0001969375, "loss": 0.1164, "step": 150}, {"epoch": 1.4545454545454546, "grad_norm": 0.1062423586845398, "learning_rate": 0.0001963125, "loss": 0.115, "step": 160}, {"epoch": 1.5454545454545454, "grad_norm": 0.11119356751441956, "learning_rate": 0.0001956875, "loss": 0.1139, "step": 170}, {"epoch": 1.6363636363636362, "grad_norm": 0.1291566640138626, "learning_rate": 0.0001950625, "loss": 0.1134, "step": 180}, {"epoch": 1.7272727272727273, "grad_norm": 0.14838625490665436, "learning_rate": 0.0001944375, "loss": 0.1125, "step": 190}, {"epoch": 1.8181818181818183, "grad_norm": 0.12381252646446228, "learning_rate": 0.00019381250000000003, "loss": 0.108, "step": 200}, {"epoch": 1.9090909090909092, "grad_norm": 0.12302656471729279, "learning_rate": 0.0001931875, "loss": 0.1077, "step": 210}, {"epoch": 2.0, "grad_norm": 0.16136957705020905, "learning_rate": 0.0001925625, "loss": 0.1099, "step": 220}, {"epoch": 2.090909090909091, "grad_norm": 0.14832282066345215, "learning_rate": 0.00019193750000000002, "loss": 0.1074, "step": 230}, {"epoch": 2.1818181818181817, "grad_norm": 0.1288769245147705, "learning_rate": 0.0001913125, "loss": 0.1053, "step": 240}, {"epoch": 2.2727272727272725, "grad_norm": 0.10769686847925186, "learning_rate": 0.00019068750000000002, "loss": 0.106, "step": 250}, {"epoch": 2.3636363636363638, "grad_norm": 0.08979490399360657, "learning_rate": 0.00019006250000000002, "loss": 0.1042, "step": 260}, {"epoch": 2.4545454545454546, "grad_norm": 0.09133381396532059, "learning_rate": 0.0001894375, "loss": 0.1062, "step": 270}, {"epoch": 2.5454545454545454, "grad_norm": 0.092931367456913, "learning_rate": 0.00018881250000000002, "loss": 0.1052, "step": 280}, {"epoch": 2.6363636363636362, "grad_norm": 0.09639677405357361, "learning_rate": 0.00018818750000000001, "loss": 0.1037, "step": 290}, {"epoch": 2.7272727272727275, "grad_norm": 0.11916697025299072, "learning_rate": 0.0001875625, "loss": 0.1051, "step": 300}, {"epoch": 2.8181818181818183, "grad_norm": 0.0892835259437561, "learning_rate": 0.0001869375, "loss": 0.1047, "step": 310}, {"epoch": 2.909090909090909, "grad_norm": 0.11407743394374847, "learning_rate": 0.0001863125, "loss": 0.1061, "step": 320}, {"epoch": 3.0, "grad_norm": 0.08671534061431885, "learning_rate": 0.0001856875, "loss": 0.1057, "step": 330}, {"epoch": 3.090909090909091, "grad_norm": 0.09888774901628494, "learning_rate": 0.0001850625, "loss": 0.1035, "step": 340}, {"epoch": 3.1818181818181817, "grad_norm": 0.09734006971120834, "learning_rate": 0.00018443750000000003, "loss": 0.1033, "step": 350}, {"epoch": 3.2727272727272725, "grad_norm": 0.09276440739631653, "learning_rate": 0.0001838125, "loss": 0.1023, "step": 360}, {"epoch": 3.3636363636363638, "grad_norm": 0.12036441266536713, "learning_rate": 0.0001831875, "loss": 0.1024, "step": 370}, {"epoch": 3.4545454545454546, "grad_norm": 0.10550518333911896, "learning_rate": 0.00018256250000000003, "loss": 0.1018, "step": 380}, {"epoch": 3.5454545454545454, "grad_norm": 0.1097254827618599, "learning_rate": 0.0001819375, "loss": 0.1036, "step": 390}, {"epoch": 3.6363636363636362, "grad_norm": 0.0818243995308876, "learning_rate": 0.00018131250000000002, "loss": 0.103, "step": 400}, {"epoch": 3.7272727272727275, "grad_norm": 0.08173191547393799, "learning_rate": 0.00018068750000000002, "loss": 0.1031, "step": 410}, {"epoch": 3.8181818181818183, "grad_norm": 0.09740687161684036, "learning_rate": 0.0001800625, "loss": 0.1029, "step": 420}, {"epoch": 3.909090909090909, "grad_norm": 0.08465677499771118, "learning_rate": 0.00017943750000000002, "loss": 0.1026, "step": 430}, {"epoch": 4.0, "grad_norm": 0.07185795903205872, "learning_rate": 0.00017881250000000002, "loss": 0.1032, "step": 440}, {"epoch": 4.090909090909091, "grad_norm": 0.08107969909906387, "learning_rate": 0.00017818750000000001, "loss": 0.1007, "step": 450}, {"epoch": 4.181818181818182, "grad_norm": 0.09307459741830826, "learning_rate": 0.0001775625, "loss": 0.0999, "step": 460}, {"epoch": 4.2727272727272725, "grad_norm": 0.09075990319252014, "learning_rate": 0.0001769375, "loss": 0.1014, "step": 470}, {"epoch": 4.363636363636363, "grad_norm": 0.11035653203725815, "learning_rate": 0.0001763125, "loss": 0.1017, "step": 480}, {"epoch": 4.454545454545454, "grad_norm": 0.07549503445625305, "learning_rate": 0.0001756875, "loss": 0.1019, "step": 490}, {"epoch": 4.545454545454545, "grad_norm": 0.08525212109088898, "learning_rate": 0.0001750625, "loss": 0.1028, "step": 500}, {"epoch": 4.636363636363637, "grad_norm": 0.08145884424448013, "learning_rate": 0.0001744375, "loss": 0.1009, "step": 510}, {"epoch": 4.7272727272727275, "grad_norm": 0.0779726654291153, "learning_rate": 0.0001738125, "loss": 0.1004, "step": 520}, {"epoch": 4.818181818181818, "grad_norm": 0.08844465017318726, "learning_rate": 0.0001731875, "loss": 0.1014, "step": 530}, {"epoch": 4.909090909090909, "grad_norm": 0.0693427100777626, "learning_rate": 0.0001725625, "loss": 0.1022, "step": 540}, {"epoch": 5.0, "grad_norm": 0.09574601799249649, "learning_rate": 0.00017193750000000003, "loss": 0.1026, "step": 550}, {"epoch": 5.090909090909091, "grad_norm": 0.09986525774002075, "learning_rate": 0.0001713125, "loss": 0.1006, "step": 560}, {"epoch": 5.181818181818182, "grad_norm": 0.08585955202579498, "learning_rate": 0.0001706875, "loss": 0.1018, "step": 570}, {"epoch": 5.2727272727272725, "grad_norm": 0.07690086215734482, "learning_rate": 0.00017006250000000002, "loss": 0.0996, "step": 580}, {"epoch": 5.363636363636363, "grad_norm": 0.08414618670940399, "learning_rate": 0.0001694375, "loss": 0.1002, "step": 590}, {"epoch": 5.454545454545454, "grad_norm": 0.07607945054769516, "learning_rate": 0.00016881250000000002, "loss": 0.1014, "step": 600}, {"epoch": 5.545454545454545, "grad_norm": 0.08244053274393082, "learning_rate": 0.00016818750000000002, "loss": 0.1006, "step": 610}, {"epoch": 5.636363636363637, "grad_norm": 0.07578031718730927, "learning_rate": 0.0001675625, "loss": 0.1008, "step": 620}, {"epoch": 5.7272727272727275, "grad_norm": 0.08184117823839188, "learning_rate": 0.0001669375, "loss": 0.1007, "step": 630}, {"epoch": 5.818181818181818, "grad_norm": 0.09670000523328781, "learning_rate": 0.0001663125, "loss": 0.1012, "step": 640}, {"epoch": 5.909090909090909, "grad_norm": 0.08012593537569046, "learning_rate": 0.0001656875, "loss": 0.1002, "step": 650}, {"epoch": 6.0, "grad_norm": 0.09731986373662949, "learning_rate": 0.0001650625, "loss": 0.1019, "step": 660}, {"epoch": 6.090909090909091, "grad_norm": 0.06632432341575623, "learning_rate": 0.0001644375, "loss": 0.0997, "step": 670}, {"epoch": 6.181818181818182, "grad_norm": 0.08964743465185165, "learning_rate": 0.0001638125, "loss": 0.0995, "step": 680}, {"epoch": 6.2727272727272725, "grad_norm": 0.09099581092596054, "learning_rate": 0.0001631875, "loss": 0.0999, "step": 690}, {"epoch": 6.363636363636363, "grad_norm": 0.07612790167331696, "learning_rate": 0.00016256250000000003, "loss": 0.1011, "step": 700}, {"epoch": 6.454545454545454, "grad_norm": 0.07699596136808395, "learning_rate": 0.0001619375, "loss": 0.1002, "step": 710}, {"epoch": 6.545454545454545, "grad_norm": 0.07864248007535934, "learning_rate": 0.0001613125, "loss": 0.0997, "step": 720}, {"epoch": 6.636363636363637, "grad_norm": 0.09346223622560501, "learning_rate": 0.00016068750000000002, "loss": 0.1013, "step": 730}, {"epoch": 6.7272727272727275, "grad_norm": 0.0742068812251091, "learning_rate": 0.0001600625, "loss": 0.1021, "step": 740}, {"epoch": 6.818181818181818, "grad_norm": 0.06872671097517014, "learning_rate": 0.00015943750000000002, "loss": 0.1013, "step": 750}, {"epoch": 6.909090909090909, "grad_norm": 0.06783844530582428, "learning_rate": 0.00015881250000000002, "loss": 0.1008, "step": 760}, {"epoch": 7.0, "grad_norm": 0.11063265800476074, "learning_rate": 0.0001581875, "loss": 0.1004, "step": 770}, {"epoch": 7.090909090909091, "grad_norm": 0.07406746596097946, "learning_rate": 0.00015756250000000001, "loss": 0.1005, "step": 780}, {"epoch": 7.181818181818182, "grad_norm": 0.07343527674674988, "learning_rate": 0.0001569375, "loss": 0.1001, "step": 790}, {"epoch": 7.2727272727272725, "grad_norm": 0.06928499042987823, "learning_rate": 0.0001563125, "loss": 0.0987, "step": 800}, {"epoch": 7.363636363636363, "grad_norm": 0.081618532538414, "learning_rate": 0.0001556875, "loss": 0.1004, "step": 810}, {"epoch": 7.454545454545454, "grad_norm": 0.07569947093725204, "learning_rate": 0.0001550625, "loss": 0.0995, "step": 820}, {"epoch": 7.545454545454545, "grad_norm": 0.07172151654958725, "learning_rate": 0.0001544375, "loss": 0.0999, "step": 830}, {"epoch": 7.636363636363637, "grad_norm": 0.06206684187054634, "learning_rate": 0.0001538125, "loss": 0.1009, "step": 840}, {"epoch": 7.7272727272727275, "grad_norm": 0.07179193943738937, "learning_rate": 0.00015318750000000003, "loss": 0.0993, "step": 850}, {"epoch": 7.818181818181818, "grad_norm": 0.07970451563596725, "learning_rate": 0.0001525625, "loss": 0.0999, "step": 860}, {"epoch": 7.909090909090909, "grad_norm": 0.07562630623579025, "learning_rate": 0.0001519375, "loss": 0.0996, "step": 870}, {"epoch": 8.0, "grad_norm": 0.09682547301054001, "learning_rate": 0.00015131250000000003, "loss": 0.0996, "step": 880}, {"epoch": 8.090909090909092, "grad_norm": 0.06837032735347748, "learning_rate": 0.0001506875, "loss": 0.0987, "step": 890}, {"epoch": 8.181818181818182, "grad_norm": 0.06757349520921707, "learning_rate": 0.00015006250000000002, "loss": 0.099, "step": 900}, {"epoch": 8.272727272727273, "grad_norm": 0.11275411397218704, "learning_rate": 0.00014943750000000002, "loss": 0.0999, "step": 910}, {"epoch": 8.363636363636363, "grad_norm": 0.07859168946743011, "learning_rate": 0.0001488125, "loss": 0.0989, "step": 920}, {"epoch": 8.454545454545455, "grad_norm": 0.06544824689626694, "learning_rate": 0.00014818750000000002, "loss": 0.1, "step": 930}, {"epoch": 8.545454545454545, "grad_norm": 0.07389603555202484, "learning_rate": 0.00014756250000000002, "loss": 0.0998, "step": 940}, {"epoch": 8.636363636363637, "grad_norm": 0.060512885451316833, "learning_rate": 0.00014693750000000001, "loss": 0.1008, "step": 950}, {"epoch": 8.727272727272727, "grad_norm": 0.07693391293287277, "learning_rate": 0.0001463125, "loss": 0.0993, "step": 960}, {"epoch": 8.818181818181818, "grad_norm": 0.06425317376852036, "learning_rate": 0.0001456875, "loss": 0.0994, "step": 970}, {"epoch": 8.909090909090908, "grad_norm": 0.06763262301683426, "learning_rate": 0.0001450625, "loss": 0.1, "step": 980}, {"epoch": 9.0, "grad_norm": 0.06646216660737991, "learning_rate": 0.0001444375, "loss": 0.0999, "step": 990}, {"epoch": 9.090909090909092, "grad_norm": 0.0798954963684082, "learning_rate": 0.0001438125, "loss": 0.0984, "step": 1000}, {"epoch": 9.181818181818182, "grad_norm": 0.07820896059274673, "learning_rate": 0.0001431875, "loss": 0.0995, "step": 1010}, {"epoch": 9.272727272727273, "grad_norm": 0.07971404492855072, "learning_rate": 0.0001425625, "loss": 0.1004, "step": 1020}, {"epoch": 9.363636363636363, "grad_norm": 0.0709853395819664, "learning_rate": 0.0001419375, "loss": 0.0989, "step": 1030}, {"epoch": 9.454545454545455, "grad_norm": 0.06809371709823608, "learning_rate": 0.0001413125, "loss": 0.0977, "step": 1040}, {"epoch": 9.545454545454545, "grad_norm": 0.08191844075918198, "learning_rate": 0.00014068750000000002, "loss": 0.0999, "step": 1050}, {"epoch": 9.636363636363637, "grad_norm": 0.06777837872505188, "learning_rate": 0.0001400625, "loss": 0.0983, "step": 1060}, {"epoch": 9.727272727272727, "grad_norm": 0.07983691990375519, "learning_rate": 0.0001394375, "loss": 0.1, "step": 1070}, {"epoch": 9.818181818181818, "grad_norm": 0.06531688570976257, "learning_rate": 0.00013881250000000002, "loss": 0.0996, "step": 1080}, {"epoch": 9.909090909090908, "grad_norm": 0.09025952219963074, "learning_rate": 0.0001381875, "loss": 0.0992, "step": 1090}, {"epoch": 10.0, "grad_norm": 0.06777399778366089, "learning_rate": 0.00013756250000000002, "loss": 0.1006, "step": 1100}, {"epoch": 10.090909090909092, "grad_norm": 0.06805805116891861, "learning_rate": 0.00013693750000000001, "loss": 0.0977, "step": 1110}, {"epoch": 10.181818181818182, "grad_norm": 0.07283280044794083, "learning_rate": 0.00013631249999999999, "loss": 0.0986, "step": 1120}, {"epoch": 10.272727272727273, "grad_norm": 0.06333229690790176, "learning_rate": 0.0001356875, "loss": 0.0987, "step": 1130}, {"epoch": 10.363636363636363, "grad_norm": 0.06656196713447571, "learning_rate": 0.0001350625, "loss": 0.0992, "step": 1140}, {"epoch": 10.454545454545455, "grad_norm": 0.06905560195446014, "learning_rate": 0.0001344375, "loss": 0.0978, "step": 1150}, {"epoch": 10.545454545454545, "grad_norm": 0.06155708059668541, "learning_rate": 0.0001338125, "loss": 0.0981, "step": 1160}, {"epoch": 10.636363636363637, "grad_norm": 0.06011158227920532, "learning_rate": 0.0001331875, "loss": 0.1, "step": 1170}, {"epoch": 10.727272727272727, "grad_norm": 0.06577569246292114, "learning_rate": 0.0001325625, "loss": 0.0997, "step": 1180}, {"epoch": 10.818181818181818, "grad_norm": 0.06259054690599442, "learning_rate": 0.0001319375, "loss": 0.0984, "step": 1190}, {"epoch": 10.909090909090908, "grad_norm": 0.0657447874546051, "learning_rate": 0.00013131250000000003, "loss": 0.0986, "step": 1200}, {"epoch": 11.0, "grad_norm": 0.09398005902767181, "learning_rate": 0.0001306875, "loss": 0.0995, "step": 1210}, {"epoch": 11.090909090909092, "grad_norm": 0.05611197277903557, "learning_rate": 0.0001300625, "loss": 0.0977, "step": 1220}, {"epoch": 11.181818181818182, "grad_norm": 0.07141376286745071, "learning_rate": 0.00012943750000000002, "loss": 0.0984, "step": 1230}, {"epoch": 11.272727272727273, "grad_norm": 0.06833440810441971, "learning_rate": 0.0001288125, "loss": 0.0986, "step": 1240}, {"epoch": 11.363636363636363, "grad_norm": 0.06712725013494492, "learning_rate": 0.00012818750000000002, "loss": 0.098, "step": 1250}, {"epoch": 11.454545454545455, "grad_norm": 0.06561345607042313, "learning_rate": 0.00012756250000000002, "loss": 0.0983, "step": 1260}, {"epoch": 11.545454545454545, "grad_norm": 0.06883208453655243, "learning_rate": 0.0001269375, "loss": 0.0985, "step": 1270}, {"epoch": 11.636363636363637, "grad_norm": 0.08128055185079575, "learning_rate": 0.00012631250000000001, "loss": 0.0991, "step": 1280}, {"epoch": 11.727272727272727, "grad_norm": 0.07574495673179626, "learning_rate": 0.0001256875, "loss": 0.0988, "step": 1290}, {"epoch": 11.818181818181818, "grad_norm": 0.060866326093673706, "learning_rate": 0.0001250625, "loss": 0.0977, "step": 1300}, {"epoch": 11.909090909090908, "grad_norm": 0.06317364424467087, "learning_rate": 0.0001244375, "loss": 0.0982, "step": 1310}, {"epoch": 12.0, "grad_norm": 0.06791232526302338, "learning_rate": 0.0001238125, "loss": 0.0983, "step": 1320}, {"epoch": 12.090909090909092, "grad_norm": 0.07862011343240738, "learning_rate": 0.0001231875, "loss": 0.0966, "step": 1330}, {"epoch": 12.181818181818182, "grad_norm": 0.09421858936548233, "learning_rate": 0.0001225625, "loss": 0.0962, "step": 1340}, {"epoch": 12.272727272727273, "grad_norm": 0.06554871052503586, "learning_rate": 0.00012193750000000002, "loss": 0.0981, "step": 1350}, {"epoch": 12.363636363636363, "grad_norm": 0.06289142370223999, "learning_rate": 0.0001213125, "loss": 0.0972, "step": 1360}, {"epoch": 12.454545454545455, "grad_norm": 0.06881557404994965, "learning_rate": 0.0001206875, "loss": 0.0981, "step": 1370}, {"epoch": 12.545454545454545, "grad_norm": 0.08674079179763794, "learning_rate": 0.00012006250000000001, "loss": 0.0977, "step": 1380}, {"epoch": 12.636363636363637, "grad_norm": 0.06540089100599289, "learning_rate": 0.0001194375, "loss": 0.0986, "step": 1390}, {"epoch": 12.727272727272727, "grad_norm": 0.0613732784986496, "learning_rate": 0.00011881250000000002, "loss": 0.0983, "step": 1400}], "logging_steps": 10, "max_steps": 3300, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 6.451880800671744e+16, "train_batch_size": 8, "trial_name": null, "trial_params": null}