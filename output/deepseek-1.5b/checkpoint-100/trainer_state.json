{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.45454545454545453, "eval_steps": 500, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.045454545454545456, "grad_norm": 0.3358898460865021, "learning_rate": 0.0019972727272727273, "loss": 1.0189, "step": 10}, {"epoch": 0.09090909090909091, "grad_norm": 0.26215052604675293, "learning_rate": 0.001994242424242424, "loss": 0.1878, "step": 20}, {"epoch": 0.13636363636363635, "grad_norm": 0.11077727377414703, "learning_rate": 0.001991212121212121, "loss": 0.1576, "step": 30}, {"epoch": 0.18181818181818182, "grad_norm": 0.16624949872493744, "learning_rate": 0.001988181818181818, "loss": 0.1349, "step": 40}, {"epoch": 0.22727272727272727, "grad_norm": 0.12547656893730164, "learning_rate": 0.0019851515151515153, "loss": 0.1321, "step": 50}, {"epoch": 0.2727272727272727, "grad_norm": 0.0680217295885086, "learning_rate": 0.0019821212121212124, "loss": 0.1239, "step": 60}, {"epoch": 0.3181818181818182, "grad_norm": 0.06775681674480438, "learning_rate": 0.001979090909090909, "loss": 0.121, "step": 70}, {"epoch": 0.36363636363636365, "grad_norm": 0.08839734643697739, "learning_rate": 0.001976060606060606, "loss": 0.12, "step": 80}, {"epoch": 0.4090909090909091, "grad_norm": 0.07873207330703735, "learning_rate": 0.0019730303030303033, "loss": 0.1227, "step": 90}, {"epoch": 0.45454545454545453, "grad_norm": 0.09993688017129898, "learning_rate": 0.00197, "loss": 0.1315, "step": 100}], "logging_steps": 10, "max_steps": 6600, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2288904776294400.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}