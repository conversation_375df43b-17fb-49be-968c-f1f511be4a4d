{"add_bos_token": true, "add_eos_token": false, "add_prefix_space": null, "added_tokens_decoder": {"151643": {"content": "<｜end▁of▁sentence｜>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151644": {"content": "<｜User｜>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151645": {"content": "<｜Assistant｜>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151646": {"content": "<｜begin▁of▁sentence｜>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151647": {"content": "<|EOT|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151648": {"content": "<think>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151649": {"content": "</think>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151650": {"content": "<|quad_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151651": {"content": "<|quad_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151652": {"content": "<|vision_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151653": {"content": "<|vision_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151654": {"content": "<|vision_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151655": {"content": "<|image_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151656": {"content": "<|video_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151657": {"content": "<tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151658": {"content": "</tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151659": {"content": "<|fim_prefix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151660": {"content": "<|fim_middle|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151661": {"content": "<|fim_suffix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151662": {"content": "<|fim_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151663": {"content": "<|repo_name|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151664": {"content": "<|file_sep|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}}, "bos_token": "<｜begin▁of▁sentence｜>", "clean_up_tokenization_spaces": false, "eos_token": "<｜end▁of▁sentence｜>", "extra_special_tokens": {}, "legacy": true, "model_max_length": 16384, "pad_token": "<｜end▁of▁sentence｜>", "sp_model_kwargs": {}, "tokenizer_class": "LlamaTokenizerFast", "unk_token": null, "use_default_system_prompt": false}