{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 6.363636363636363, "eval_steps": 500, "global_step": 700, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09090909090909091, "grad_norm": 1.6590996980667114, "learning_rate": 1.8e-05, "loss": 3.6295, "step": 10}, {"epoch": 0.18181818181818182, "grad_norm": 1.3075464963912964, "learning_rate": 3.8e-05, "loss": 3.3717, "step": 20}, {"epoch": 0.2727272727272727, "grad_norm": 1.2319278717041016, "learning_rate": 5.8e-05, "loss": 2.5538, "step": 30}, {"epoch": 0.36363636363636365, "grad_norm": 1.3520307540893555, "learning_rate": 7.800000000000001e-05, "loss": 1.5233, "step": 40}, {"epoch": 0.45454545454545453, "grad_norm": 0.4957904815673828, "learning_rate": 9.8e-05, "loss": 0.6023, "step": 50}, {"epoch": 0.5454545454545454, "grad_norm": 0.4895520806312561, "learning_rate": 0.000118, "loss": 0.3425, "step": 60}, {"epoch": 0.6363636363636364, "grad_norm": 0.3014693856239319, "learning_rate": 0.000138, "loss": 0.2546, "step": 70}, {"epoch": 0.7272727272727273, "grad_norm": 0.29924941062927246, "learning_rate": 0.00015800000000000002, "loss": 0.2088, "step": 80}, {"epoch": 0.8181818181818182, "grad_norm": 0.39131468534469604, "learning_rate": 0.00017800000000000002, "loss": 0.1593, "step": 90}, {"epoch": 0.9090909090909091, "grad_norm": 0.1685856431722641, "learning_rate": 0.00019800000000000002, "loss": 0.1384, "step": 100}, {"epoch": 1.0, "grad_norm": 0.20438167452812195, "learning_rate": 0.00019943750000000002, "loss": 0.1349, "step": 110}, {"epoch": 1.0909090909090908, "grad_norm": 0.15389294922351837, "learning_rate": 0.00019881250000000001, "loss": 0.126, "step": 120}, {"epoch": 1.1818181818181819, "grad_norm": 0.1302276849746704, "learning_rate": 0.0001981875, "loss": 0.1194, "step": 130}, {"epoch": 1.2727272727272727, "grad_norm": 0.1432138979434967, "learning_rate": 0.0001975625, "loss": 0.1206, "step": 140}, {"epoch": 1.3636363636363638, "grad_norm": 0.21154679358005524, "learning_rate": 0.0001969375, "loss": 0.1162, "step": 150}, {"epoch": 1.4545454545454546, "grad_norm": 0.10943342745304108, "learning_rate": 0.0001963125, "loss": 0.1149, "step": 160}, {"epoch": 1.5454545454545454, "grad_norm": 0.12745453417301178, "learning_rate": 0.0001956875, "loss": 0.114, "step": 170}, {"epoch": 1.6363636363636362, "grad_norm": 0.12182550877332687, "learning_rate": 0.0001950625, "loss": 0.1129, "step": 180}, {"epoch": 1.7272727272727273, "grad_norm": 0.15101772546768188, "learning_rate": 0.0001944375, "loss": 0.112, "step": 190}, {"epoch": 1.8181818181818183, "grad_norm": 0.12651965022087097, "learning_rate": 0.00019381250000000003, "loss": 0.1082, "step": 200}, {"epoch": 1.9090909090909092, "grad_norm": 0.12422076612710953, "learning_rate": 0.0001931875, "loss": 0.1075, "step": 210}, {"epoch": 2.0, "grad_norm": 0.1499999314546585, "learning_rate": 0.0001925625, "loss": 0.1096, "step": 220}, {"epoch": 2.090909090909091, "grad_norm": 0.12513941526412964, "learning_rate": 0.00019193750000000002, "loss": 0.1069, "step": 230}, {"epoch": 2.1818181818181817, "grad_norm": 0.1204308420419693, "learning_rate": 0.0001913125, "loss": 0.105, "step": 240}, {"epoch": 2.2727272727272725, "grad_norm": 0.11119510233402252, "learning_rate": 0.00019068750000000002, "loss": 0.1059, "step": 250}, {"epoch": 2.3636363636363638, "grad_norm": 0.08938981592655182, "learning_rate": 0.00019006250000000002, "loss": 0.1039, "step": 260}, {"epoch": 2.4545454545454546, "grad_norm": 0.08979173749685287, "learning_rate": 0.0001894375, "loss": 0.1061, "step": 270}, {"epoch": 2.5454545454545454, "grad_norm": 0.10796014964580536, "learning_rate": 0.00018881250000000002, "loss": 0.1052, "step": 280}, {"epoch": 2.6363636363636362, "grad_norm": 0.1131565123796463, "learning_rate": 0.00018818750000000001, "loss": 0.1036, "step": 290}, {"epoch": 2.7272727272727275, "grad_norm": 0.11254740506410599, "learning_rate": 0.0001875625, "loss": 0.105, "step": 300}, {"epoch": 2.8181818181818183, "grad_norm": 0.09119588136672974, "learning_rate": 0.0001869375, "loss": 0.1045, "step": 310}, {"epoch": 2.909090909090909, "grad_norm": 0.11186566203832626, "learning_rate": 0.0001863125, "loss": 0.106, "step": 320}, {"epoch": 3.0, "grad_norm": 0.08803427964448929, "learning_rate": 0.0001856875, "loss": 0.1056, "step": 330}, {"epoch": 3.090909090909091, "grad_norm": 0.09982631355524063, "learning_rate": 0.0001850625, "loss": 0.1036, "step": 340}, {"epoch": 3.1818181818181817, "grad_norm": 0.08548197150230408, "learning_rate": 0.00018443750000000003, "loss": 0.1033, "step": 350}, {"epoch": 3.2727272727272725, "grad_norm": 0.09299783408641815, "learning_rate": 0.0001838125, "loss": 0.1023, "step": 360}, {"epoch": 3.3636363636363638, "grad_norm": 0.12178279459476471, "learning_rate": 0.0001831875, "loss": 0.1023, "step": 370}, {"epoch": 3.4545454545454546, "grad_norm": 0.1038268432021141, "learning_rate": 0.00018256250000000003, "loss": 0.1018, "step": 380}, {"epoch": 3.5454545454545454, "grad_norm": 0.11435266584157944, "learning_rate": 0.0001819375, "loss": 0.1038, "step": 390}, {"epoch": 3.6363636363636362, "grad_norm": 0.08239200711250305, "learning_rate": 0.00018131250000000002, "loss": 0.1031, "step": 400}, {"epoch": 3.7272727272727275, "grad_norm": 0.08368169516324997, "learning_rate": 0.00018068750000000002, "loss": 0.1028, "step": 410}, {"epoch": 3.8181818181818183, "grad_norm": 0.0957045778632164, "learning_rate": 0.0001800625, "loss": 0.1028, "step": 420}, {"epoch": 3.909090909090909, "grad_norm": 0.08919887989759445, "learning_rate": 0.00017943750000000002, "loss": 0.1027, "step": 430}, {"epoch": 4.0, "grad_norm": 0.07664839923381805, "learning_rate": 0.00017881250000000002, "loss": 0.1034, "step": 440}, {"epoch": 4.090909090909091, "grad_norm": 0.08126307278871536, "learning_rate": 0.00017818750000000001, "loss": 0.1006, "step": 450}, {"epoch": 4.181818181818182, "grad_norm": 0.09815125912427902, "learning_rate": 0.0001775625, "loss": 0.0998, "step": 460}, {"epoch": 4.2727272727272725, "grad_norm": 0.09313257038593292, "learning_rate": 0.0001769375, "loss": 0.1014, "step": 470}, {"epoch": 4.363636363636363, "grad_norm": 0.16307874023914337, "learning_rate": 0.0001763125, "loss": 0.102, "step": 480}, {"epoch": 4.454545454545454, "grad_norm": 0.07879184186458588, "learning_rate": 0.0001756875, "loss": 0.1021, "step": 490}, {"epoch": 4.545454545454545, "grad_norm": 0.09497516602277756, "learning_rate": 0.0001750625, "loss": 0.1029, "step": 500}, {"epoch": 4.636363636363637, "grad_norm": 0.08163068443536758, "learning_rate": 0.0001744375, "loss": 0.1011, "step": 510}, {"epoch": 4.7272727272727275, "grad_norm": 0.08758193999528885, "learning_rate": 0.0001738125, "loss": 0.1008, "step": 520}, {"epoch": 4.818181818181818, "grad_norm": 0.08619612455368042, "learning_rate": 0.0001731875, "loss": 0.1035, "step": 530}, {"epoch": 4.909090909090909, "grad_norm": 0.10469002276659012, "learning_rate": 0.0001725625, "loss": 0.1038, "step": 540}, {"epoch": 5.0, "grad_norm": 0.09601987153291702, "learning_rate": 0.00017193750000000003, "loss": 0.103, "step": 550}, {"epoch": 5.090909090909091, "grad_norm": 0.09856659173965454, "learning_rate": 0.0001713125, "loss": 0.1006, "step": 560}, {"epoch": 5.181818181818182, "grad_norm": 0.08968719840049744, "learning_rate": 0.0001706875, "loss": 0.1017, "step": 570}, {"epoch": 5.2727272727272725, "grad_norm": 0.07981737703084946, "learning_rate": 0.00017006250000000002, "loss": 0.0999, "step": 580}, {"epoch": 5.363636363636363, "grad_norm": 0.08600102365016937, "learning_rate": 0.0001694375, "loss": 0.1005, "step": 590}, {"epoch": 5.454545454545454, "grad_norm": 0.07774714380502701, "learning_rate": 0.00016881250000000002, "loss": 0.1017, "step": 600}, {"epoch": 5.545454545454545, "grad_norm": 0.11841742694377899, "learning_rate": 0.00016818750000000002, "loss": 0.1006, "step": 610}, {"epoch": 5.636363636363637, "grad_norm": 0.10372190177440643, "learning_rate": 0.0001675625, "loss": 0.1013, "step": 620}, {"epoch": 5.7272727272727275, "grad_norm": 0.08029806613922119, "learning_rate": 0.0001669375, "loss": 0.1009, "step": 630}, {"epoch": 5.818181818181818, "grad_norm": 0.10751544684171677, "learning_rate": 0.0001663125, "loss": 0.1012, "step": 640}, {"epoch": 5.909090909090909, "grad_norm": 0.08756457269191742, "learning_rate": 0.0001656875, "loss": 0.1003, "step": 650}, {"epoch": 6.0, "grad_norm": 0.09541923552751541, "learning_rate": 0.0001650625, "loss": 0.102, "step": 660}, {"epoch": 6.090909090909091, "grad_norm": 0.06596691161394119, "learning_rate": 0.0001644375, "loss": 0.1, "step": 670}, {"epoch": 6.181818181818182, "grad_norm": 0.0907755047082901, "learning_rate": 0.0001638125, "loss": 0.0997, "step": 680}, {"epoch": 6.2727272727272725, "grad_norm": 0.09658453613519669, "learning_rate": 0.0001631875, "loss": 0.1, "step": 690}, {"epoch": 6.363636363636363, "grad_norm": 0.08613882213830948, "learning_rate": 0.00016256250000000003, "loss": 0.1011, "step": 700}], "logging_steps": 10, "max_steps": 3300, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 3.2210832731652096e+16, "train_batch_size": 8, "trial_name": null, "trial_params": null}