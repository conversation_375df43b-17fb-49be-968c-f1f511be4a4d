{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.3636363636363638, "eval_steps": 500, "global_step": 300, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.045454545454545456, "grad_norm": 0.3358898460865021, "learning_rate": 0.0019972727272727273, "loss": 1.0189, "step": 10}, {"epoch": 0.09090909090909091, "grad_norm": 0.26215052604675293, "learning_rate": 0.001994242424242424, "loss": 0.1878, "step": 20}, {"epoch": 0.13636363636363635, "grad_norm": 0.11077727377414703, "learning_rate": 0.001991212121212121, "loss": 0.1576, "step": 30}, {"epoch": 0.18181818181818182, "grad_norm": 0.16624949872493744, "learning_rate": 0.001988181818181818, "loss": 0.1349, "step": 40}, {"epoch": 0.22727272727272727, "grad_norm": 0.12547656893730164, "learning_rate": 0.0019851515151515153, "loss": 0.1321, "step": 50}, {"epoch": 0.2727272727272727, "grad_norm": 0.0680217295885086, "learning_rate": 0.0019821212121212124, "loss": 0.1239, "step": 60}, {"epoch": 0.3181818181818182, "grad_norm": 0.06775681674480438, "learning_rate": 0.001979090909090909, "loss": 0.121, "step": 70}, {"epoch": 0.36363636363636365, "grad_norm": 0.08839734643697739, "learning_rate": 0.001976060606060606, "loss": 0.12, "step": 80}, {"epoch": 0.4090909090909091, "grad_norm": 0.07873207330703735, "learning_rate": 0.0019730303030303033, "loss": 0.1227, "step": 90}, {"epoch": 0.45454545454545453, "grad_norm": 0.09993688017129898, "learning_rate": 0.00197, "loss": 0.1315, "step": 100}, {"epoch": 0.5, "grad_norm": 0.07665016502141953, "learning_rate": 0.001966969696969697, "loss": 0.1202, "step": 110}, {"epoch": 0.5454545454545454, "grad_norm": 0.08393397927284241, "learning_rate": 0.0019639393939393937, "loss": 0.1193, "step": 120}, {"epoch": 0.5909090909090909, "grad_norm": 0.06740294396877289, "learning_rate": 0.001960909090909091, "loss": 0.1148, "step": 130}, {"epoch": 0.6363636363636364, "grad_norm": 0.11012943089008331, "learning_rate": 0.001957878787878788, "loss": 0.1149, "step": 140}, {"epoch": 0.6818181818181818, "grad_norm": 0.0816747397184372, "learning_rate": 0.001954848484848485, "loss": 0.1559, "step": 150}, {"epoch": 0.7272727272727273, "grad_norm": 236.93601989746094, "learning_rate": 0.001951818181818182, "loss": 0.2884, "step": 160}, {"epoch": 0.7727272727272727, "grad_norm": 2.2518954277038574, "learning_rate": 0.0019487878787878788, "loss": 2.4151, "step": 170}, {"epoch": 0.8181818181818182, "grad_norm": 70.4244155883789, "learning_rate": 0.0019457575757575757, "loss": 1.6187, "step": 180}, {"epoch": 0.8636363636363636, "grad_norm": 0.20761413872241974, "learning_rate": 0.0019427272727272726, "loss": 0.3236, "step": 190}, {"epoch": 0.9090909090909091, "grad_norm": 1.5080771446228027, "learning_rate": 0.0019396969696969697, "loss": 0.195, "step": 200}, {"epoch": 0.9545454545454546, "grad_norm": 2.1919124126434326, "learning_rate": 0.0019366666666666668, "loss": 0.1698, "step": 210}, {"epoch": 1.0, "grad_norm": 0.49618980288505554, "learning_rate": 0.0019336363636363637, "loss": 0.1339, "step": 220}, {"epoch": 1.0454545454545454, "grad_norm": 0.4937746822834015, "learning_rate": 0.0019306060606060606, "loss": 0.135, "step": 230}, {"epoch": 1.0909090909090908, "grad_norm": 0.25243204832077026, "learning_rate": 0.0019275757575757575, "loss": 0.1271, "step": 240}, {"epoch": 1.1363636363636362, "grad_norm": 0.13163121044635773, "learning_rate": 0.0019245454545454546, "loss": 0.1272, "step": 250}, {"epoch": 1.1818181818181819, "grad_norm": 0.1179310604929924, "learning_rate": 0.0019215151515151517, "loss": 0.1193, "step": 260}, {"epoch": 1.2272727272727273, "grad_norm": 2.031186103820801, "learning_rate": 0.0019184848484848486, "loss": 0.1394, "step": 270}, {"epoch": 1.2727272727272727, "grad_norm": 0.5335614085197449, "learning_rate": 0.0019154545454545455, "loss": 0.1689, "step": 280}, {"epoch": 1.3181818181818181, "grad_norm": 0.24057701230049133, "learning_rate": 0.0019124242424242424, "loss": 0.1455, "step": 290}, {"epoch": 1.3636363636363638, "grad_norm": 0.7257072925567627, "learning_rate": 0.0019093939393939395, "loss": 0.1309, "step": 300}], "logging_steps": 10, "max_steps": 6600, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 6899876266223616.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}