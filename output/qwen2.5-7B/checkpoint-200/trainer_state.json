{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.8181818181818183, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09090909090909091, "grad_norm": 2.481581449508667, "learning_rate": 9.972727272727273e-05, "loss": 1.9541, "step": 10}, {"epoch": 0.18181818181818182, "grad_norm": 0.4319668114185333, "learning_rate": 9.942424242424243e-05, "loss": 0.2736, "step": 20}, {"epoch": 0.2727272727272727, "grad_norm": 0.2722488045692444, "learning_rate": 9.912121212121213e-05, "loss": 0.0832, "step": 30}, {"epoch": 0.36363636363636365, "grad_norm": 0.27446916699409485, "learning_rate": 9.881818181818182e-05, "loss": 0.0537, "step": 40}, {"epoch": 0.45454545454545453, "grad_norm": 0.26728397607803345, "learning_rate": 9.851515151515151e-05, "loss": 0.0514, "step": 50}, {"epoch": 0.5454545454545454, "grad_norm": 0.2211197018623352, "learning_rate": 9.821212121212122e-05, "loss": 0.0428, "step": 60}, {"epoch": 0.6363636363636364, "grad_norm": 0.19066886603832245, "learning_rate": 9.790909090909091e-05, "loss": 0.0379, "step": 70}, {"epoch": 0.7272727272727273, "grad_norm": 0.19557419419288635, "learning_rate": 9.760606060606062e-05, "loss": 0.0404, "step": 80}, {"epoch": 0.8181818181818182, "grad_norm": 0.20548030734062195, "learning_rate": 9.730303030303031e-05, "loss": 0.0356, "step": 90}, {"epoch": 0.9090909090909091, "grad_norm": 0.1557910442352295, "learning_rate": 9.7e-05, "loss": 0.0335, "step": 100}, {"epoch": 1.0, "grad_norm": 0.16257937252521515, "learning_rate": 9.669696969696969e-05, "loss": 0.0335, "step": 110}, {"epoch": 1.0909090909090908, "grad_norm": 0.15229913592338562, "learning_rate": 9.63939393939394e-05, "loss": 0.033, "step": 120}, {"epoch": 1.1818181818181819, "grad_norm": 0.127852201461792, "learning_rate": 9.60909090909091e-05, "loss": 0.0307, "step": 130}, {"epoch": 1.2727272727272727, "grad_norm": 0.17033223807811737, "learning_rate": 9.57878787878788e-05, "loss": 0.0331, "step": 140}, {"epoch": 1.3636363636363638, "grad_norm": 0.14268502593040466, "learning_rate": 9.548484848484849e-05, "loss": 0.0317, "step": 150}, {"epoch": 1.4545454545454546, "grad_norm": 0.13442645967006683, "learning_rate": 9.518181818181818e-05, "loss": 0.0317, "step": 160}, {"epoch": 1.5454545454545454, "grad_norm": 0.1130542978644371, "learning_rate": 9.487878787878788e-05, "loss": 0.0315, "step": 170}, {"epoch": 1.6363636363636362, "grad_norm": 0.13939669728279114, "learning_rate": 9.457575757575759e-05, "loss": 0.0311, "step": 180}, {"epoch": 1.7272727272727273, "grad_norm": 0.12835150957107544, "learning_rate": 9.427272727272728e-05, "loss": 0.0306, "step": 190}, {"epoch": 1.8181818181818183, "grad_norm": 0.11929593980312347, "learning_rate": 9.396969696969697e-05, "loss": 0.0298, "step": 200}], "logging_steps": 10, "max_steps": 3300, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4.140521965233562e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}