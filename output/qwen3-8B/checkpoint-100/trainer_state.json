{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.9090909090909091, "eval_steps": 500, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09090909090909091, "grad_norm": 1.30715811252594, "learning_rate": 9.972727272727273e-05, "loss": 2.0935, "step": 10}, {"epoch": 0.18181818181818182, "grad_norm": 0.8757233619689941, "learning_rate": 9.942424242424243e-05, "loss": 0.5395, "step": 20}, {"epoch": 0.2727272727272727, "grad_norm": 0.45221561193466187, "learning_rate": 9.912121212121213e-05, "loss": 0.1257, "step": 30}, {"epoch": 0.36363636363636365, "grad_norm": 0.4304859936237335, "learning_rate": 9.881818181818182e-05, "loss": 0.0771, "step": 40}, {"epoch": 0.45454545454545453, "grad_norm": 0.2628391683101654, "learning_rate": 9.851515151515151e-05, "loss": 0.0537, "step": 50}, {"epoch": 0.5454545454545454, "grad_norm": 0.18741150200366974, "learning_rate": 9.821212121212122e-05, "loss": 0.0465, "step": 60}, {"epoch": 0.6363636363636364, "grad_norm": 0.15731686353683472, "learning_rate": 9.790909090909091e-05, "loss": 0.0386, "step": 70}, {"epoch": 0.7272727272727273, "grad_norm": 0.18915551900863647, "learning_rate": 9.760606060606062e-05, "loss": 0.0409, "step": 80}, {"epoch": 0.8181818181818182, "grad_norm": 0.20247450470924377, "learning_rate": 9.730303030303031e-05, "loss": 0.0363, "step": 90}, {"epoch": 0.9090909090909091, "grad_norm": 0.1467636078596115, "learning_rate": 9.7e-05, "loss": 0.0341, "step": 100}], "logging_steps": 10, "max_steps": 3300, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2.249671007030477e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}