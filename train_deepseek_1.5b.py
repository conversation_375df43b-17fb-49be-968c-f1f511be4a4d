# -*- coding: utf-8 -*-
import os
import torch
import pandas as pd
from datasets import Dataset
from peft import LoraConfig, TaskType, get_peft_model
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from swanlab.integration.huggingface import SwanLabCallback
import swanlab

# ✅ 设备设置
os.environ["CUDA_VISIBLE_DEVICES"] = "5"
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"✅ 使用设备: {device}")

# ✅ 模型路径和训练集路径
model_dir = "/opt/LLM_MODEL/deepseek_1.3b/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B/"
train_path = "./training_dataset_2000.json"  # 你的AI客服训练数据路径

# 在 imports 后立即添加
print("✅ 环境检查开始")
print(f"PyTorch 版本: {torch.__version__}")
print(f"CUDA 可用: {torch.cuda.is_available()}")
print(f"当前设备: {device}")
print(f"可见 GPU: {os.environ.get('CUDA_VISIBLE_DEVICES')}")

if not os.path.exists(train_path):
    raise FileNotFoundError(f"训练数据不存在: {train_path}")


# ✅ 加载 tokenizer 和模型
tokenizer = AutoTokenizer.from_pretrained(model_dir, use_fast=False, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(model_dir, device_map="auto", torch_dtype=torch.bfloat16)
model.enable_input_require_grads()

# ✅ 数据预处理函数
def process_func(example):
    prompt = tokenizer.apply_chat_template([
        {"role": "system", "content": example["instruction"]},
        {"role": "user", "content": example["input"]},
        {"role": "assistant", "content": example["output"]},
    ], tokenize=False, add_generation_prompt=False)

    tokenized = tokenizer(prompt, truncation=True, max_length=1024)
    input_ids = tokenized["input_ids"]
    attention_mask = tokenized["attention_mask"]
    labels = input_ids.copy()

    return {
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "labels": labels
    }

# ✅ 加载数据
df = pd.read_json(train_path)
ds = Dataset.from_pandas(df)
train_dataset = ds.map(process_func, remove_columns=ds.column_names, num_proc=4)

# ✅ LoRA 配置 - 针对DeepSeek模型优化
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    inference_mode=False,
    r=16,  # 对于较小的模型，可以使用稍大的r值
    lora_alpha=32,
    lora_dropout=0.1,
)
model = get_peft_model(model, lora_config)

# ✅ 训练参数 - 针对1.5B模型优化
train_args = TrainingArguments(
    output_dir="./output/deepseek-1.5b",
    per_device_train_batch_size=8,  # 1.5B模型可以使用更大的batch size
    per_device_eval_batch_size=8,   # 每个设备上的评估批量大小
    #gradient_accumulation_steps=2,  # 相应减少梯度累积步数
    logging_steps=10,
    num_train_epochs=30,
    save_steps=100,
    learning_rate=2e-3,  # 对于较小模型可以使用稍大的学习率
    save_on_each_node=True,
    gradient_checkpointing=True,
    report_to="none",
    #warmup_steps=100,  # 添加warmup步数
    weight_decay=0.01,  # 添加权重衰减
)

# ✅ SwanLab 日志记录
swanlab_callback = SwanLabCallback(
    project="DeepSeek",
    experiment_name="DeepSeek-R1-Distill-Qwen-1.5B-FunctionCalling",
    description="使用 DeepSeek-R1-Distill-Qwen-1.5B 微调意图识别",
    config={
        "model": "DeepSeek-R1-Distill-Qwen-1.5B",
        "dataset": "training_dataset_2000.json",
        "lora_r": 16,
        "learning_rate": 2e-4,
        "batch_size": 8,
    }
)

# ✅ 启动训练
trainer = Trainer(
    model=model,
    args=train_args,
    train_dataset=train_dataset,
    data_collator=DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),
    callbacks=[swanlab_callback],
)

trainer.train()
swanlab.finish()
