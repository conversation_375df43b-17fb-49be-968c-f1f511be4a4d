# Qwen2.5-7B vLLM性能对比工具

## 概述

本工具基于原始的 `qwen2.5-7B-model_comparison.py` 创建，使用vLLM框架进行高性能推理，对比原始模型和微调模型的性能表现。

## 文件说明

- `qwen2.5-7B-vllm_comparison.py` - 主要的vLLM性能对比脚本
- `test_vllm_setup.py` - vLLM环境测试脚本
- `vLLM_comparison_README.md` - 本说明文档

## 主要改进

### 1. 使用vLLM框架
- 替换transformers的推理方式为vLLM高性能推理
- 支持张量并行（tensor parallelism）加速
- 优化GPU内存使用

### 2. 性能优化特性
- **批量推理**: vLLM支持高效的批量处理
- **连续批处理**: 动态批处理优化吞吐量
- **KV缓存优化**: PagedAttention技术减少内存碎片
- **张量并行**: 多GPU并行推理

### 3. LoRA支持
- 支持动态加载LoRA适配器
- 无需合并权重，直接使用微调检查点

### 4. 详细性能统计
- 单次推理时间对比
- 平均推理时间统计
- JSON格式成功率对比
- 参数提取准确率对比
- 性能提升百分比计算

## 环境要求

### 必需依赖
```bash
pip install vllm
pip install transformers
pip install torch
pip install peft
```

### GPU要求
- CUDA兼容的GPU
- 建议使用多张GPU以获得最佳性能
- 足够的GPU内存（建议16GB+）

## 使用方法

### 1. 环境测试
首先运行环境测试脚本确保所有依赖正确安装：

```bash
python test_vllm_setup.py
```

### 2. 运行性能对比
```bash
python qwen2.5-7B-vllm_comparison.py
```

### 3. 交互式测试
程序启动后，您可以输入智能家居控制指令进行测试：

```
请输入指令: 打开客厅的灯
请输入指令: 把卧室空调温度调到26度
请输入指令: quit  # 退出程序
```

## 性能对比指标

### 1. 推理速度
- 单次推理时间（秒）
- 平均推理时间
- 性能提升倍数

### 2. 输出质量
- JSON格式正确性
- Function调用结构完整性
- 参数提取准确率

### 3. 总体统计
- 测试次数统计
- 成功率对比
- 性能提升百分比

## 配置说明

### GPU配置
```python
os.environ["CUDA_VISIBLE_DEVICES"] = "5,6"  # 指定使用的GPU
```

### vLLM参数
```python
LLM(
    model=model_path,
    tensor_parallel_size=2,      # 张量并行大小
    trust_remote_code=True,
    dtype="float16",             # 使用半精度加速
    gpu_memory_utilization=0.8,  # GPU内存使用率
    max_model_len=4096          # 最大序列长度
)
```

### 采样参数
```python
SamplingParams(
    temperature=0.0,    # 确定性输出
    max_tokens=256,     # 最大生成token数
    stop_token_ids=[...]  # 停止token
)
```

## 预期性能提升

使用vLLM相比原始transformers推理，预期可获得：

- **推理速度**: 2-10x提升（取决于硬件配置）
- **内存效率**: 显著减少GPU内存使用
- **吞吐量**: 大幅提升并发处理能力

## 故障排除

### 常见问题

1. **vLLM导入失败**
   ```bash
   pip install vllm --upgrade
   ```

2. **CUDA内存不足**
   - 减少 `gpu_memory_utilization` 参数
   - 减少 `max_model_len` 参数
   - 使用更少的GPU

3. **LoRA加载失败**
   - 确认微调模型路径正确
   - 检查LoRA适配器文件完整性

4. **推理速度没有提升**
   - 确认使用了多GPU并行
   - 检查GPU利用率
   - 调整批处理大小

## 注意事项

1. **内存管理**: vLLM会预分配GPU内存，确保有足够的显存
2. **模型兼容性**: 确保模型与vLLM版本兼容
3. **并行配置**: 根据GPU数量调整 `tensor_parallel_size`
4. **性能监控**: 使用 `nvidia-smi` 监控GPU使用情况

## 技术细节

### vLLM优势
- **PagedAttention**: 高效的注意力机制实现
- **连续批处理**: 动态调整批处理大小
- **内存优化**: 减少内存碎片和浪费
- **并行优化**: 支持多种并行策略

### 与原版对比
- 保持相同的测试逻辑和评估指标
- 替换推理引擎为vLLM
- 增加详细的性能统计
- 支持LoRA动态加载

## 扩展功能

可以进一步扩展的功能：
- 批量测试数据集
- 更多性能指标统计
- 不同并行配置对比
- 内存使用分析
- 延迟分布统计
