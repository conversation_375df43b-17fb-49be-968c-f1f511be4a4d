#!/usr/bin/env python3
"""
Qwen3-8B高级测试脚本
使用官方方法防止思考输出，直接生成JSON结果
"""

import json
import time
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, StoppingCriteria, StoppingCriteriaList
from peft import PeftModel

# 指定使用6号GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "6"

def add_qwen3_support():
    """添加Qwen3模型支持"""
    try:
        from transformers import AutoConfig, AutoModelForCausalLM
        from transformers.models.qwen2 import Qwen2Config, Qwen2ForCausalLM
        
        class Qwen3Config(Qwen2Config):
            model_type = "qwen3"
            def __init__(self, **kwargs):
                super().__init__(**kwargs)
        
        class Qwen3ForCausalLM(Qwen2ForCausalLM):
            config_class = Qwen3Config
            def __init__(self, config):
                super().__init__(config)
        
        AutoConfig.register("qwen3", Qwen3Config)
        AutoModelForCausalLM.register(Qwen3Config, Qwen3ForCausalLM)
        
        print("✅ 成功添加Qwen3支持")
        return True
    except Exception as e:
        print(f"⚠️  Qwen3支持添加警告: {e}")
        return True

class ThinkStoppingCriteria(StoppingCriteria):
    """自定义停止条件：遇到<think>标签时停止生成"""
    
    def __init__(self, tokenizer):
        self.tokenizer = tokenizer
        # 获取<think>标签的token id
        self.think_tokens = tokenizer.encode("<think>", add_special_tokens=False)
        self.think_length = len(self.think_tokens)
    
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor, **kwargs) -> bool:
        # 检查最后生成的tokens是否包含<think>
        if input_ids.shape[1] >= self.think_length:
            last_tokens = input_ids[0, -self.think_length:].tolist()
            if last_tokens == self.think_tokens:
                return True
        return False

def advanced_test():
    """高级测试函数 - 使用多种方法防止思考输出"""
    print("🚀 Qwen3-8B高级推理测试（防止思考输出）")
    print("=" * 60)
    
    add_qwen3_support()
    
    # 模型路径
    base_model_path = "/opt/LLM_MODEL/Qwen3-8B/Qwen/Qwen3-8B/"
    finetuned_model_path = "/opt/fuyu/test/output/qwen3-8B/checkpoint-500"
    
    try:
        print("📥 正在加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            base_model_path, 
            use_fast=False, 
            trust_remote_code=True
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            tokenizer.pad_token_id = tokenizer.eos_token_id
        
        print("📥 正在加载微调模型...")
        base_for_finetuned = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            device_map="auto"
        )
        finetuned_model = PeftModel.from_pretrained(
            base_for_finetuned, 
            finetuned_model_path
        )
        
        print("✅ 模型加载完成！")
        
        # 测试用例
        test_cases = [
            "关闭客厅灯",
            "帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式",
            "调节书房空调温度到25度",
            "查询今天天气"
        ]
        
        # 优化的系统提示词 - 使用强制格式
        system_prompt = """你是智能家居控制助手。

重要：你必须立即以JSON格式回复，不要有任何前缀、思考过程或解释。

输出格式：{"function_calls": [{"name": "函数名", "arguments": {"intent": "意图", "domain": "设备类型", "room": "房间", "device": "设备名"}}]}

支持的函数：
- openOrClose：开关控制
- setHighOrLow：调节控制
- scene：场景控制
- getWeather：天气查询
- chat：闲聊对话

立即输出JSON，不要任何其他内容："""

        def test_with_multiple_methods(model, test_input, model_name):
            print(f"\n🧪 测试{model_name} - 输入: {test_input}")
            
            # 使用Few-shot示例来强化直接输出JSON的行为
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": "打开客厅灯"},
                {"role": "assistant", "content": '{"function_calls": [{"name": "openOrClose", "arguments": {"intent": "打开灯", "domain": "灯", "room": "客厅", "device": ""}}]}'},
                {"role": "user", "content": "调高卧室灯亮度"},
                {"role": "assistant", "content": '{"function_calls": [{"name": "setHighOrLow", "arguments": {"intent": "调高亮度", "domain": "灯", "room": "卧室", "device": ""}}]}'},
                {"role": "user", "content": test_input}
            ]
            
            text = tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            model_inputs = tokenizer([text], return_tensors="pt")
            device = next(model.parameters()).device
            model_inputs = {k: v.to(device) for k, v in model_inputs.items()}
            
            if 'attention_mask' not in model_inputs:
                model_inputs['attention_mask'] = torch.ones_like(model_inputs['input_ids'])
            
            # 创建停止条件
            stopping_criteria = StoppingCriteriaList([ThinkStoppingCriteria(tokenizer)])
            
            start_time = time.time()
            
            with torch.no_grad():
                # 方法1：使用自定义停止条件
                try:
                    generated_ids = model.generate(
                        input_ids=model_inputs['input_ids'],
                        attention_mask=model_inputs['attention_mask'],
                        max_new_tokens=256,
                        do_sample=False,
                        pad_token_id=tokenizer.pad_token_id,
                        eos_token_id=tokenizer.eos_token_id,
                        repetition_penalty=1.1,
                        use_cache=True,
                        stopping_criteria=stopping_criteria,  # 自定义停止条件
                        early_stopping=True
                    )
                    print("✅ 使用自定义停止条件")
                except Exception as e:
                    print(f"⚠️  自定义停止条件失败，使用标准方法: {e}")
                    generated_ids = model.generate(
                        input_ids=model_inputs['input_ids'],
                        attention_mask=model_inputs['attention_mask'],
                        max_new_tokens=256,
                        do_sample=False,
                        pad_token_id=tokenizer.pad_token_id,
                        eos_token_id=tokenizer.eos_token_id,
                        repetition_penalty=1.1,
                        use_cache=True
                    )
            
            # 解码
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs['input_ids'], generated_ids)
            ]
            
            response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            inference_time = time.time() - start_time
            
            # 智能清理输出
            cleaned_response = smart_clean_response(response)
            
            print(f"原始输出: {response}")
            print(f"清理后输出: {cleaned_response}")
            print(f"推理时间: {inference_time:.3f}s")
            
            # 验证JSON
            try:
                json.loads(cleaned_response)
                print("✅ JSON格式正确")
                return cleaned_response, inference_time, True
            except:
                print("❌ JSON格式错误")
                return cleaned_response, inference_time, False

        def smart_clean_response(response: str) -> str:
            """智能清理响应"""
            import re
            
            # 1. 移除think标签及其内容
            cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL).strip()
            
            # 2. 如果遇到<think>开始但没有结束，截断到<think>之前
            if '<think>' in cleaned:
                cleaned = cleaned.split('<think>')[0].strip()
            
            # 3. 提取JSON部分
            json_match = re.search(r'\{.*\}', cleaned, flags=re.DOTALL)
            if json_match:
                cleaned = json_match.group(0)
            
            # 4. 清理多余空白
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
            
            return cleaned
        
        # 运行测试
        for test_input in test_cases:
            result, time_taken, is_valid = test_with_multiple_methods(
                finetuned_model, test_input, "微调模型"
            )
            
            print(f"📊 结果: {'✅ 成功' if is_valid else '❌ 失败'}")
            print("-" * 60)
        
        print("\n🎉 高级测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    advanced_test()
