import json
import pandas as pd
import torch
import torch.distributed as dist
from datasets import Dataset, DatasetDict
from transformers import AutoTokenizer
from peft import LoraConfig, TaskType, get_peft_model
from transformers import AutoModelForCausalLM, TrainingArguments, Trainer, DataCollatorForSeq2Seq
import os
import random
import numpy as np
import argparse

# 分布式训练配置 - 指定使用GPU 5、6、7
os.environ["CUDA_VISIBLE_DEVICES"] = "5,6,7"

def setup_distributed():
    """初始化分布式训练环境"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
    else:
        # 如果没有设置环境变量，使用默认值
        rank = 0
        world_size = 1
        local_rank = 0

    # 初始化分布式进程组
    if world_size > 1:
        dist.init_process_group(backend='nccl')
        torch.cuda.set_device(local_rank)
        print(f"分布式训练初始化完成: rank={rank}, world_size={world_size}, local_rank={local_rank}")
    else:
        print("单卡训练模式")

    return rank, world_size, local_rank

def cleanup_distributed():
    """清理分布式训练环境"""
    if dist.is_initialized():
        dist.destroy_process_group()

# 修复transformers库的ALL_PARALLEL_STYLES问题
from transformers import modeling_utils
if not hasattr(modeling_utils, "ALL_PARALLEL_STYLES") or modeling_utils.ALL_PARALLEL_STYLES is None:
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]

# 添加Qwen3支持 - 改进版本
def setup_qwen3_support():
    """为当前transformers版本添加Qwen3支持，包含权重处理优化"""
    try:
        from transformers import AutoConfig, AutoModelForCausalLM
        from transformers.models.auto.configuration_auto import CONFIG_MAPPING
        from transformers.models.auto.modeling_auto import MODEL_FOR_CAUSAL_LM_MAPPING
        import torch.nn as nn
        import warnings

        # 检查是否已经支持qwen3
        if "qwen3" in CONFIG_MAPPING:
            print("✅ Qwen3已经被支持")
            return True

        # 尝试使用Qwen2的配置和模型类来支持Qwen3
        try:
            from transformers.models.qwen2.configuration_qwen2 import Qwen2Config
            from transformers.models.qwen2.modeling_qwen2 import Qwen2ForCausalLM

            # 创建Qwen3配置类（基于Qwen2）
            class Qwen3Config(Qwen2Config):
                model_type = "qwen3"

                def __init__(self, **kwargs):
                    super().__init__(**kwargs)
                    # 添加Qwen3特有的配置处理
                    if hasattr(self, 'use_qk_norm'):
                        # 如果有qk_norm配置，记录但不使用
                        warnings.warn("Qwen3的qk_norm特性在Qwen2架构中不支持，将被忽略", UserWarning)

            class Qwen3ForCausalLM(Qwen2ForCausalLM):
                config_class = Qwen3Config

                def _init_weights(self, module):
                    """改进的权重初始化，提高数值稳定性"""
                    # 使用父类的标准初始化，不进行额外修改
                    super()._init_weights(module)

            # 注册Qwen3配置和模型
            CONFIG_MAPPING.register("qwen3", Qwen3Config)
            MODEL_FOR_CAUSAL_LM_MAPPING.register(Qwen3Config, Qwen3ForCausalLM)

            print("✅ 成功添加Qwen3支持（基于Qwen2架构，包含稳定性优化）")
            return True

        except ImportError as e:
            print(f"❌ 无法导入Qwen2模块: {e}")
            return False

    except Exception as e:
        print(f"❌ 设置Qwen3支持时出错: {e}")
        return False

# 在导入时设置Qwen3支持
setup_qwen3_support()

# 尝试导入swanlab，如果不存在则跳过
try:
    # 尝试新版本的导入路径
    try:
        from swanlab.integration.transformers import SwanLabCallback
    except ImportError:
        # 如果新版本不可用，使用旧版本
        from swanlab.integration.huggingface import SwanLabCallback

    import swanlab

    # 配置SwanLab API Key
    swanlab.login(api_key="isz7UzJcOkzt1v3JAGQpb")
    print("✅ SwanLab API认证成功")

    SWANLAB_AVAILABLE = True
except ImportError:
    print("警告: SwanLab未安装，将跳过实验跟踪功能")
    SWANLAB_AVAILABLE = False
except Exception as e:
    print(f"警告: SwanLab API认证失败: {e}")
    print("将继续训练但跳过SwanLab跟踪功能")
    SWANLAB_AVAILABLE = False


def set_seed(seed=42):
    """设置随机种子以确保可重现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def load_and_split_dataset(dataset_path, train_ratio=0.8, seed=42):
    """
    加载数据集并按比例划分为训练集和验证集
    """
    set_seed(seed)

    # 读取JSON文件
    with open(dataset_path, "r", encoding="utf-8") as file:
        data = json.load(file)

    # 随机打乱数据
    random.shuffle(data)

    # 计算划分点
    total_size = len(data)
    train_size = int(total_size * train_ratio)

    # 划分数据集
    train_data = data[:train_size]
    val_data = data[train_size:]

    print(f"数据集总数: {total_size}")
    print(f"训练集数量: {len(train_data)}")
    print(f"验证集数量: {len(val_data)}")

    return train_data, val_data


def dataset_jsonl_transfer(data_list, new_path):
    """
    将数据列表转换为JSONL格式并保存
    """
    # 保存为JSONL文件
    with open(new_path, "w", encoding="utf-8") as file:
        for message in data_list:
            file.write(json.dumps(message, ensure_ascii=False) + "\n")
            
            
def create_process_func(tokenizer):
    """
    创建数据预处理函数的工厂函数
    """
    def process_func(example):
        """
        将数据集进行预处理
        """
        MAX_LENGTH = 256  # 减少序列长度节省显存
        input_ids, attention_mask, labels = [], [], []
        instruction = tokenizer(
            f"<|im_start|>system\n{example['instruction']}<|im_end|>\n<|im_start|>user\n{example['input']}<|im_end|>\n<|im_start|>assistant\n",
            add_special_tokens=False,
        )
        response = tokenizer(f"{example['output']}", add_special_tokens=False)
        input_ids = instruction["input_ids"] + response["input_ids"] + [tokenizer.pad_token_id]
        attention_mask = (
            instruction["attention_mask"] + response["attention_mask"] + [1]
        )
        labels = [-100] * len(instruction["input_ids"]) + response["input_ids"] + [tokenizer.pad_token_id]
        if len(input_ids) > MAX_LENGTH:  # 做一个截断
            input_ids = input_ids[:MAX_LENGTH]
            attention_mask = attention_mask[:MAX_LENGTH]
            labels = labels[:MAX_LENGTH]
        return {"input_ids": input_ids, "attention_mask": attention_mask, "labels": labels}
    return process_func


def predict(messages, model, tokenizer):
    device = "cuda"
    text = tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=True
    )
    model_inputs = tokenizer([text], return_tensors="pt").to(device)

    generated_ids = model.generate(
        model_inputs.input_ids,
        max_new_tokens=512
    )
    generated_ids = [
        output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
    ]
    
    response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
    
    print(response)
     
    return response
    
def main():
    """主训练函数"""
    # 初始化分布式训练
    rank, world_size, local_rank = setup_distributed()

    # 设置模型路径 - 使用您指定的Qwen3-8B模型
    model_path = "/opt/LLM_MODEL/Qwen3-8B/Qwen/Qwen3-8B/"

    # 只在主进程中打印信息
    if rank == 0:
        print("正在加载模型和tokenizer...")

    # 加载tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False, trust_remote_code=True)

    # 为分布式训练加载模型 - 平衡稳定性和显存使用
    if world_size > 1:
        # 多卡分布式训练 - 使用float32全精度
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,  # 使用float16节省显存
            trust_remote_code=True,
            low_cpu_mem_usage=True,  # 启用低内存模式节省显存
            device_map=None  # 分布式训练时不使用device_map
        )
        # 使用to_empty()方法来避免meta tensor问题
        try:
            model = model.to(f'cuda:{local_rank}')
        except NotImplementedError:
            # 如果遇到meta tensor问题，使用to_empty()
            model = model.to_empty(device=f'cuda:{local_rank}')
    else:
        # 单卡训练
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,  # 使用float16节省显存
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            device_map="auto"
        )

    model.enable_input_require_grads()  # 开启梯度检查点时，要执行该方法

    # 加载和划分数据集
    dataset_path = "./training_dataset_2000.json"
    if rank == 0:
        print("正在加载和划分数据集...")
    train_data, val_data = load_and_split_dataset(dataset_path, train_ratio=0.8, seed=42)

    # 保存划分后的数据集为JSONL格式
    train_jsonl_path = "train_split.jsonl"
    val_jsonl_path = "val_split.jsonl"

    dataset_jsonl_transfer(train_data, train_jsonl_path)
    dataset_jsonl_transfer(val_data, val_jsonl_path)

    # 创建数据预处理函数
    process_func = create_process_func(tokenizer)

    # 创建训练集和验证集
    if rank == 0:
        print("正在处理训练数据...")
    train_df = pd.read_json(train_jsonl_path, lines=True)
    train_ds = Dataset.from_pandas(train_df)
    train_dataset = train_ds.map(process_func, remove_columns=train_ds.column_names)

    if rank == 0:
        print("正在处理验证数据...")
    val_df = pd.read_json(val_jsonl_path, lines=True)
    val_ds = Dataset.from_pandas(val_df)
    val_dataset = val_ds.map(process_func, remove_columns=val_ds.column_names)

    # 配置LoRA - 使用更保守的参数提高稳定性
    config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,  # 训练模式
        r=4,  # 降低LoRA秩提高稳定性
        lora_alpha=8,  # 降低alpha值，减少梯度幅度
        lora_dropout=0.1,  # 增加dropout防止过拟合
        bias="none",  # 不训练bias
        use_rslora=False,  # 禁用RSLoRA
        init_lora_weights="gaussian",  # 使用高斯初始化
    )

    model = get_peft_model(model, config)

    # 配置训练参数 - 支持分布式训练，增强数值稳定性
    model_name = "Qwen3-8B"  # 用于区分保存路径
    args = TrainingArguments(
        output_dir=f"./output/{model_name}",
        per_device_train_batch_size=1,  # 保持最小batch size
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=8,  # 减少梯度累积步数节省显存
        logging_steps=5,  # 更频繁的日志记录
        num_train_epochs=2,
        save_steps=50,  # 更频繁的保存
        eval_steps=50,
        eval_strategy="steps",
        save_strategy="steps",
        learning_rate=1e-4,  # 降低学习率提高稳定性
        weight_decay=0.01,  # 添加权重衰减
        warmup_steps=20,  # 增加预热步数，确保稳定启动
        warmup_ratio=0.1,  # 添加预热比例
        lr_scheduler_type="linear",  # 使用线性学习率调度，更稳定
        save_on_each_node=True,
        gradient_checkpointing=True,
        report_to="none",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        # 数值稳定性配置 - 禁用混合精度
        fp16=False,  # 禁用fp16，使用全精度训练
        bf16=False,  # 不使用bf16
        max_grad_norm=0.5,  # 更严格的梯度裁剪
        dataloader_drop_last=True,  # 丢弃最后不完整的batch
        # 分布式训练配置
        ddp_find_unused_parameters=False,
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        # 添加更多稳定性选项
        skip_memory_metrics=True,
        log_level="warning",  # 减少日志噪音
        # 优化器配置
        optim="adamw_torch",  # 使用标准AdamW
        adam_beta1=0.9,
        adam_beta2=0.999,
        adam_epsilon=1e-8,
    )

    # 配置SwanLab回调（如果可用）
    callbacks = []
    if SWANLAB_AVAILABLE and rank == 0:  # 只在主进程中使用SwanLab
        swanlab_callback = SwanLabCallback(
            project="Qwen3-fintune",
            experiment_name="Qwen3-8B-SmartHome-Distributed",
            description="使用通义千问Qwen3-8B模型在智能家居控制数据集上进行分布式微调。",
            config={
                "model": "Qwen/Qwen3-8B",
                "dataset": "training_dataset_2000.json",
                "train_size": len(train_data),
                "val_size": len(val_data),
                "task": "智能家居控制助手",
                "world_size": world_size,
                "gpus": "5,6,7",
            }
        )
        callbacks.append(swanlab_callback)

    # 创建训练器
    trainer = Trainer(
        model=model,
        args=args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),
        callbacks=callbacks,
    )

    # 开始训练
    if rank == 0:
        print("开始分布式训练...")
    trainer.train()

    # 只在主进程中进行测试
    if rank == 0:
        print("训练完成，开始测试模型...")
        # 用验证集的前10条，测试模型
        test_df = pd.read_json(val_jsonl_path, lines=True)[:10]

        test_text_list = []
        for index, row in test_df.iterrows():
            instruction = row['instruction']
            input_value = row['input']

            messages = [
                {"role": "system", "content": f"{instruction}"},
                {"role": "user", "content": f"{input_value}"}
            ]

            response = predict(messages, model, tokenizer)
            messages.append({"role": "assistant", "content": f"{response}"})
            result_text = f"{messages[0]}\n\n{messages[1]}\n\n{messages[2]}"

            if SWANLAB_AVAILABLE:
                test_text_list.append(swanlab.Text(result_text, caption=response))
            else:
                print(f"测试样例 {index + 1}:")
                print(f"输入: {input_value}")
                print(f"预测输出: {response}")
                print("-" * 50)

        if SWANLAB_AVAILABLE:
            swanlab.log({"Prediction": test_text_list})
            swanlab.finish()
        else:
            print("训练完成！由于未安装SwanLab，跳过了实验跟踪功能。")

    # 清理分布式训练环境
    cleanup_distributed()


if __name__ == "__main__":
    main()
