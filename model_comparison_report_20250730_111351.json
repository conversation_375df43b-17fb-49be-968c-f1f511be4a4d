{"timestamp": "2025-07-30T11:13:51.226316", "test_samples": 5, "base_model_stats": {"json_success_rate": 1.0, "function_success_rate": 1.0, "avg_parameter_score": 0.8, "avg_response_time": 1.8522660732269287, "error_rate": 0.0}, "finetuned_model_stats": {"json_success_rate": 1.0, "function_success_rate": 1.0, "avg_parameter_score": 0.8666666666666666, "avg_response_time": 2.2319961547851563, "error_rate": 0.0}, "improvements": {"json_improvement": 0.0, "function_improvement": 0.0, "parameter_improvement": 0.06666666666666654, "speed_improvement": -0.20500838785902945, "error_reduction": 0.0}, "detailed_results": {"base_model": {"responses": ["{\n  \"function_calls\": [\n    {\n      \"name\": \"openOrClose\",\n      \"arguments\": {\n        \"intent\": \"关闭灯\",\n        \"domain\": \"灯\",\n        \"room\": \"客厅\",\n        \"device\": \"\"\n      }\n    }\n  ]\n}", "{\n  \"function_calls\": [\n    {\n      \"name\": \"setHighOrLow\",\n      \"arguments\": {\n        \"intent\": \"设置亮度\",\n        \"domain\": \"灯\",\n        \"value\": \"80%\",\n        \"room\": \"卧室\",\n        \"device\": \"\"\n      }\n    }\n  ]\n}", "{\n  \"function_calls\": [\n    {\n      \"name\": \"openOrClose\",\n      \"arguments\": {\n        \"intent\": \"关闭空调\",\n        \"domain\": \"空调\",\n        \"room\": \"书房\",\n        \"device\": \"\"\n      }\n    },\n    {\n      \"name\": \"scene\",\n      \"arguments\": {\n        \"intent\": \"打开场景\",\n        \"domain\": \"场景\",\n        \"scene\": \"睡眠模式\"\n      }\n    }\n  ]\n}", "{\n  \"function_calls\": [\n    {\n      \"name\": \"chat\",\n      \"arguments\": {\n        \"intent\": \"自我介绍\",\n        \"content\": \"您好！我是您的智能家居控制助手，可以帮您管理家中的各种智能设备。请问有什么可以帮助您的？\"\n      }\n    }\n  ]\n}", "{\n  \"function_calls\": [\n    {\n      \"name\": \"getWeat<PERSON>\",\n      \"arguments\": {\n        \"intent\": \"查询天气状况\",\n        \"domain\": \"\",\n        \"pos\": \"伦敦\",\n        \"unit\": \"day\"\n      }\n    }\n  ]\n}"], "times": [1.9794692993164062, 1.6867210865020752, 2.4476122856140137, 1.6658124923706055, 1.481715202331543], "json_valid": [true, true, true, true, true], "function_valid": [true, true, true, true, true], "parameter_scores": [1.0, 1.0, 1.0, 0.3333333333333333, 0.6666666666666666], "errors": [false, false, false, false, false]}, "finetuned_model": {"responses": ["{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"\"}}]}", "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"\"}}]}", "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}", "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\", \"domain\": \"\"}}]}", "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"], "times": [1.863379716873169, 2.1822187900543213, 3.0470049381256104, 2.021146059036255, 2.046231269836426], "json_valid": [true, true, true, true, true], "function_valid": [true, true, true, true, true], "parameter_scores": [1.0, 1.0, 1.0, 0.6666666666666666, 0.6666666666666666], "errors": [false, false, false, false, false]}, "test_cases": [{"input": "关闭客厅灯", "expected_output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"\"}}]}", "base_response": "{\n  \"function_calls\": [\n    {\n      \"name\": \"openOrClose\",\n      \"arguments\": {\n        \"intent\": \"关闭灯\",\n        \"domain\": \"灯\",\n        \"room\": \"客厅\",\n        \"device\": \"\"\n      }\n    }\n  ]\n}", "finetuned_response": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"\"}}]}", "base_time": 1.9794692993164062, "finetuned_time": 1.863379716873169}, {"input": "调节卧室灯光亮度到80%", "expected_output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\", \"value\": \"80\"}}]}", "base_response": "{\n  \"function_calls\": [\n    {\n      \"name\": \"setHighOrLow\",\n      \"arguments\": {\n        \"intent\": \"设置亮度\",\n        \"domain\": \"灯\",\n        \"value\": \"80%\",\n        \"room\": \"卧室\",\n        \"device\": \"\"\n      }\n    }\n  ]\n}", "finetuned_response": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"value\": \"80\", \"room\": \"卧室\", \"device\": \"\"}}]}", "base_time": 1.6867210865020752, "finetuned_time": 2.1822187900543213}, {"input": "关闭书房空调，然后启动睡眠模式", "expected_output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}", "base_response": "{\n  \"function_calls\": [\n    {\n      \"name\": \"openOrClose\",\n      \"arguments\": {\n        \"intent\": \"关闭空调\",\n        \"domain\": \"空调\",\n        \"room\": \"书房\",\n        \"device\": \"\"\n      }\n    },\n    {\n      \"name\": \"scene\",\n      \"arguments\": {\n        \"intent\": \"打开场景\",\n        \"domain\": \"场景\",\n        \"scene\": \"睡眠模式\"\n      }\n    }\n  ]\n}", "finetuned_response": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}", "base_time": 2.4476122856140137, "finetuned_time": 3.0470049381256104}, {"input": "你好", "expected_output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\"}}]}", "base_response": "{\n  \"function_calls\": [\n    {\n      \"name\": \"chat\",\n      \"arguments\": {\n        \"intent\": \"自我介绍\",\n        \"content\": \"您好！我是您的智能家居控制助手，可以帮您管理家中的各种智能设备。请问有什么可以帮助您的？\"\n      }\n    }\n  ]\n}", "finetuned_response": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\", \"domain\": \"\"}}]}", "base_time": 1.6658124923706055, "finetuned_time": 2.021146059036255}, {"input": "查询天气", "expected_output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}", "base_response": "{\n  \"function_calls\": [\n    {\n      \"name\": \"getWeat<PERSON>\",\n      \"arguments\": {\n        \"intent\": \"查询天气状况\",\n        \"domain\": \"\",\n        \"pos\": \"伦敦\",\n        \"unit\": \"day\"\n      }\n    }\n  ]\n}", "finetuned_response": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}", "base_time": 1.481715202331543, "finetuned_time": 2.046231269836426}]}}