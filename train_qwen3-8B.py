# -*- coding: utf-8 -*-
import os
import torch
import pandas as pd
from datasets import Dataset
from peft import LoraConfig, TaskType, get_peft_model
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from swanlab.integration.huggingface import SwanLabCallback
import swanlab

# ✅ 设备设置
os.environ["CUDA_VISIBLE_DEVICES"] = "5"
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"✅ 使用设备: {device}")

# ✅ 模型路径和训练集路径
model_dir = "/opt/LLM_MODEL/Qwen3-8B/Qwen/Qwen3-8B/"
train_path = "./training_dataset_2000.json"  # 你的AI客服训练数据路径

# ✅ 加载 tokenizer 和模型
tokenizer = AutoTokenizer.from_pretrained(model_dir, use_fast=False, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(model_dir, device_map="auto", torch_dtype=torch.bfloat16)
model.enable_input_require_grads()

# ✅ 数据预处理函数
def process_func(example):
    prompt = tokenizer.apply_chat_template([
        {"role": "system", "content": example["instruction"]},
        {"role": "user", "content": example["input"]},
        {"role": "assistant", "content": example["output"]},
    ], tokenize=False, add_generation_prompt=False)

    tokenized = tokenizer(prompt, truncation=True, max_length=1024)
    input_ids = tokenized["input_ids"]
    attention_mask = tokenized["attention_mask"]
    labels = input_ids.copy()

    return {
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "labels": labels
    }

# ✅ 加载数据
df = pd.read_json(train_path)
ds = Dataset.from_pandas(df)
train_dataset = ds.map(process_func, remove_columns=ds.column_names, num_proc=4)

# ✅ LoRA 配置
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    inference_mode=False,
    r=8,
    lora_alpha=32,
    lora_dropout=0.1,
)
model = get_peft_model(model, lora_config)

# ✅ 训练参数
train_args = TrainingArguments(
    output_dir="./output/qwen3-8B",
    per_device_train_batch_size=4,
    gradient_accumulation_steps=4,
    logging_steps=10,
    num_train_epochs=30,
    save_steps=100,
    learning_rate=1e-4,
    save_on_each_node=True,
    gradient_checkpointing=True,
    report_to="none",
)

# ✅ SwanLab 日志记录
swanlab_callback = SwanLabCallback(
    project="Qwen3",
    experiment_name="Qwen3-8B-FunctionCalling",
    description="使用 Qwen3-8B-Instruct 微调意图识别",
    config={
        "model": "Qwen3-8B-Instruct",
        "dataset": "training_dataset_2000.json"
    }
)

# ✅ 启动训练
trainer = Trainer(
    model=model,
    args=train_args,
    train_dataset=train_dataset,
    data_collator=DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),
    callbacks=[swanlab_callback],
)

trainer.train()
swanlab.finish()
