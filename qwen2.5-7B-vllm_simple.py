#!/usr/bin/env python3
"""
Qwen2.5-7B-Instruct智能家居控制模型vLLM简化版性能对比脚本
使用单GPU配置避免多进程问题
"""

import json
import time
import os
import torch
from typing import Dict, List, Tuple, Any
from transformers import AutoTokenizer
from vllm import LLM, SamplingParams

os.environ["CUDA_VISIBLE_DEVICES"] = "5"  # 使用单GPU

class SimpleVLLMComparator:
    def __init__(self):
        """初始化简化版vLLM模型对比器"""
        self.base_model_path = "/opt/LLM_MODEL/Qwen2.5-7B-Instruct/qwen/Qwen2.5-7B-Instruct/"
        
        # 简化的系统提示词
        self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   - room 参数必须精确匹配用户明确提到的位置词
   - 未提及位置时返回"默认"
   - 禁止位置推断

3. 每个数组元素是一个合法的函数调用对象，包含name和arguments字段。

4. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

示例输入："打开客厅的灯"
示例输出：
{
  "function_calls": [
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开灯",
        "domain": "灯",
        "room": "客厅",
        "device": ""
      }
    }
  ]
}"""

        print("🚀 正在初始化简化版vLLM模型对比器...")
        self.load_model()
        
    def load_model(self):
        """加载vLLM模型"""
        try:
            print("📥 正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_path, 
                use_fast=False, 
                trust_remote_code=True
            )
            
            print("📥 正在加载vLLM模型...")
            # 优化的单GPU高性能配置
            self.model = LLM(
                model=self.base_model_path,
                tensor_parallel_size=1,  # 单GPU（避免Ray问题）
                trust_remote_code=True,
                dtype="float16",
                gpu_memory_utilization=0.9,  # 高内存使用率
                max_model_len=2048,
                swap_space=0,  # 禁用swap以提升速度
                cpu_offload_gb=0,  # 禁用CPU卸载以提升速度
                disable_log_stats=True,  # 禁用日志统计
                enforce_eager=False,  # 启用CUDA图优化
                enable_prefix_caching=True,  # 启用前缀缓存
                max_num_batched_tokens=8192,  # 增加批处理token数
                max_num_seqs=128  # 适中的最大序列数
            )
            
            # 优化的采样参数配置
            self.sampling_params = SamplingParams(
                temperature=0.1,  # 与主脚本保持一致
                max_tokens=512,   # 确保完整输出
                skip_special_tokens=True,
                stop_token_ids=[self.tokenizer.eos_token_id] if self.tokenizer.eos_token_id else None
            )
            
            print("✅ vLLM模型加载完成！")
            
        except Exception as e:
            print(f"❌ vLLM模型加载失败: {e}")
            raise
    
    def predict_vllm(self, messages: List[Dict]) -> Tuple[str, float]:
        """使用vLLM进行预测"""
        try:
            start_time = time.time()
            
            # 构建输入文本
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # vLLM推理
            outputs = self.model.generate([text], self.sampling_params)
            response = outputs[0].outputs[0].text.strip()
            inference_time = time.time() - start_time
            
            return response, inference_time
            
        except Exception as e:
            print(f"❌ vLLM预测失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def evaluate_json_format(self, response: str) -> bool:
        """评估JSON格式正确性"""
        try:
            json.loads(response)
            return True
        except:
            return False

    def evaluate_function_calls(self, response: str) -> Dict[str, Any]:
        """评估function_calls结构正确性"""
        result = {
            "has_function_calls": False,
            "function_calls_count": 0,
            "valid_structure": False,
            "functions": []
        }

        try:
            data = json.loads(response)
            if "function_calls" in data and isinstance(data["function_calls"], list):
                result["has_function_calls"] = True
                result["function_calls_count"] = len(data["function_calls"])
                result["valid_structure"] = True

                for func_call in data["function_calls"]:
                    if isinstance(func_call, dict) and "name" in func_call and "arguments" in func_call:
                        result["functions"].append(func_call["name"])
                    else:
                        result["valid_structure"] = False
                        break
        except:
            pass

        return result

    def test_performance(self):
        """性能测试"""
        print("\n🎯 vLLM性能测试模式")
        print("=" * 60)

        # 测试用例
        test_cases = [
            "打开客厅的灯",
            "关闭卧室的空调",
            "把客厅温度调到26度",
            "打开全屋的灯",
            "启动回家模式"
        ]

        performance_stats = {
            "times": [],
            "json_success": 0,
            "total_tests": 0
        }

        for i, user_input in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}/{len(test_cases)}: {user_input}")
            print("-" * 40)

            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            # vLLM预测
            response, inference_time = self.predict_vllm(messages)
            print(f"响应时间: {inference_time:.3f}s")
            print(f"输出: {response}")

            # 评估
            json_valid = self.evaluate_json_format(response)
            func_eval = self.evaluate_function_calls(response)

            print(f"JSON格式: {'✅' if json_valid else '❌'}")
            print(f"Function调用: {'✅' if func_eval['valid_structure'] else '❌'} ({func_eval['function_calls_count']}个)")

            # 更新统计
            performance_stats["times"].append(inference_time)
            performance_stats["json_success"] += 1 if json_valid else 0
            performance_stats["total_tests"] += 1

        # 显示统计结果
        self.show_performance_summary(performance_stats)

    def show_performance_summary(self, stats: Dict):
        """显示性能总结"""
        print("\n📈 vLLM性能总结报告")
        print("=" * 60)
        
        # 时间统计
        avg_time = sum(stats["times"]) / len(stats["times"])
        min_time = min(stats["times"])
        max_time = max(stats["times"])
        
        print(f"⏱️  推理时间统计:")
        print(f"   平均时间: {avg_time:.3f}s")
        print(f"   最快时间: {min_time:.3f}s")
        print(f"   最慢时间: {max_time:.3f}s")
        
        # JSON格式成功率
        json_rate = stats["json_success"] / stats["total_tests"] * 100
        
        print(f"\n✅ JSON格式成功率: {json_rate:.1f}%")
        print(f"📊 总测试次数: {stats['total_tests']}")
        print("=" * 60)

    def interactive_test(self):
        """交互式测试"""
        print("\n🎯 交互式vLLM测试模式")
        print("输入智能家居控制指令，输入 'quit' 退出")
        print("=" * 60)

        while True:
            user_input = input("\n请输入指令: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break

            if not user_input:
                continue

            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            print(f"\n📝 用户输入: {user_input}")
            print("-" * 40)

            # vLLM预测
            response, inference_time = self.predict_vllm(messages)
            print(f"响应时间: {inference_time:.3f}s")
            print(f"输出: {response}")

            # 评估
            json_valid = self.evaluate_json_format(response)
            func_eval = self.evaluate_function_calls(response)

            print(f"JSON格式: {'✅' if json_valid else '❌'}")
            print(f"Function调用: {'✅' if func_eval['valid_structure'] else '❌'} ({func_eval['function_calls_count']}个)")
            print("=" * 60)


def main():
    """主函数"""
    print("🤖 Qwen2.5-7B-Instruct vLLM简化版性能测试工具")
    print("=" * 60)

    try:
        # 初始化简化版对比器
        comparator = SimpleVLLMComparator()

        # 选择测试模式
        print("\n请选择测试模式:")
        print("1. 自动性能测试")
        print("2. 交互式测试")
        
        choice = input("请输入选择 (1/2): ").strip()
        
        if choice == "1":
            comparator.test_performance()
        elif choice == "2":
            comparator.interactive_test()
        else:
            print("无效选择，默认运行自动性能测试")
            comparator.test_performance()

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
