#!/usr/bin/env python3
"""
Qwen3-8B快速测试脚本
快速验证基础模型和微调模型的推理能力
"""

import json
import time
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

# 指定使用6号GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "6"

def add_qwen3_support():
    """添加Qwen3模型支持"""
    try:
        from transformers import AutoConfig, AutoModelForCausalLM
        from transformers.models.qwen2 import Qwen2Config, Qwen2ForCausalLM
        
        # 创建Qwen3配置类，基于Qwen2
        class Qwen3Config(Qwen2Config):
            model_type = "qwen3"
            
            def __init__(self, **kwargs):
                super().__init__(**kwargs)
        
        # 创建Qwen3模型类，基于Qwen2
        class Qwen3ForCausalLM(Qwen2ForCausalLM):
            config_class = Qwen3Config
            
            def __init__(self, config):
                super().__init__(config)
        
        # 注册Qwen3配置和模型类
        AutoConfig.register("qwen3", Qwen3Config)
        AutoModelForCausalLM.register(Qwen3Config, Qwen3ForCausalLM)
        
        print("✅ 成功添加Qwen3支持")
        return True
    except Exception as e:
        print(f"⚠️  Qwen3支持添加警告: {e}")
        return True  # 继续执行，可能已经注册过了

def clean_response(response: str) -> str:
    """清理模型响应，移除think标签等无关内容，只保留JSON"""
    import re

    # 移除think标签及其内容
    cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL).strip()

    # 移除可能的多余空行和空白字符
    cleaned = re.sub(r'\n\s*\n', '\n', cleaned).strip()

    # 尝试提取JSON部分
    # 查找第一个 { 到最后一个 } 之间的内容
    json_match = re.search(r'\{.*\}', cleaned, flags=re.DOTALL)
    if json_match:
        cleaned = json_match.group(0)

    # 移除可能的前后多余文本
    cleaned = cleaned.strip()

    return cleaned

def quick_test():
    """快速测试函数"""
    print("🚀 Qwen3-8B快速推理测试")
    print("=" * 50)
    
    # 添加Qwen3支持
    add_qwen3_support()
    
    # 模型路径
    base_model_path = "/opt/LLM_MODEL/Qwen3-8B/Qwen/Qwen3-8B/"
    finetuned_model_path = "/opt/fuyu/test/output/qwen3-8B/checkpoint-500"
    
    try:
        print("📥 正在加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            base_model_path, 
            use_fast=False, 
            trust_remote_code=True
        )
        
        # 确保pad_token设置正确
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            tokenizer.pad_token_id = tokenizer.eos_token_id

        # 检查当前的chat template
        print(f"📋 当前Chat Template: {tokenizer.chat_template}")

        # 添加think标记到停止词汇表
        if hasattr(tokenizer, 'add_special_tokens'):
            special_tokens = {"additional_special_tokens": ["<think>", "</think>"]}
            tokenizer.add_special_tokens(special_tokens)
        
        print("📥 正在加载基础模型...")
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            device_map="auto"
        )
        
        print("📥 正在加载微调模型...")
        base_for_finetuned = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            device_map="auto"
        )
        finetuned_model = PeftModel.from_pretrained(
            base_for_finetuned, 
            finetuned_model_path
        )
        
        print("✅ 模型加载完成！")
        
        # 测试用例
        test_input = "关闭客厅灯"
        system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 输出必须是标准JSON对象，包含"function_calls"字段，值为JSON数组
2. 设备控制强制规则：
   - room参数必须精确匹配用户明确提到的位置词
   - 未提及位置时返回"默认"
   - 禁止位置推断（如"床头灯"不能推断为"卧室"）
   - 仅当用户明确说"全屋"、"所有设备"时才返回"all"
3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）
4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型
5. 输出纯JSON格式，不带解释、注释或任何思考过程
6. 禁止输出任何形式的思考过程，包括但不限于 <think>、<reasoning>、<analysis> 等标签"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": test_input}
        ]
        
        def test_model(model, model_name):
            print(f"\n🧪 测试{model_name}...")
            print(f"输入: {test_input}")
            
            start_time = time.time()
            
            # 构建输入
            text = tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # 编码
            model_inputs = tokenizer([text], return_tensors="pt")
            # 将输入移动到模型所在的设备
            device = next(model.parameters()).device
            model_inputs = {k: v.to(device) for k, v in model_inputs.items()}
            
            # 确保有attention_mask
            if 'attention_mask' not in model_inputs:
                model_inputs['attention_mask'] = torch.ones_like(model_inputs['input_ids'])

            # 生成 - 添加停止条件
            with torch.no_grad():
                # 方法1：使用stop_strings (如果支持)
                try:
                    generated_ids = model.generate(
                        input_ids=model_inputs['input_ids'],
                        attention_mask=model_inputs['attention_mask'],
                        max_new_tokens=128,
                        do_sample=False,
                        pad_token_id=tokenizer.pad_token_id,
                        eos_token_id=tokenizer.eos_token_id,
                        repetition_penalty=1.1,
                        use_cache=True,
                        stop_strings=["<think>"],  # 遇到<think>立即停止
                        tokenizer=tokenizer
                    )
                    print("✅ 使用stop_strings防止思考输出")
                except Exception as e:
                    # 如果不支持stop_strings，使用传统方法
                    print(f"⚠️  stop_strings不支持，使用传统方法: {e}")
                    generated_ids = model.generate(
                        input_ids=model_inputs['input_ids'],
                        attention_mask=model_inputs['attention_mask'],
                        max_new_tokens=128,
                        do_sample=False,
                        pad_token_id=tokenizer.pad_token_id,
                        eos_token_id=tokenizer.eos_token_id,
                        repetition_penalty=1.1,
                        use_cache=True
                    )

            # 解码
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs['input_ids'], generated_ids)
            ]
            
            response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            inference_time = time.time() - start_time

            print(f"原始输出: {response}")
            print(f"推理时间: {inference_time:.3f}s")

            # 清理输出，只保留JSON部分
            cleaned_response = clean_response(response)

            print(f"清理后输出: {cleaned_response}")

            # 检查JSON格式
            try:
                json.loads(cleaned_response)
                print("✅ JSON格式正确")
                json_valid = True
            except:
                print("❌ JSON格式错误")
                json_valid = False

            return cleaned_response, inference_time
        
        # 测试基础模型
        base_response, base_time = test_model(base_model, "基础模型")
        
        # 测试微调模型
        finetuned_response, finetuned_time = test_model(finetuned_model, "微调模型")
        
        # 对比结果
        print(f"\n📊 对比结果:")
        print(f"基础模型推理时间: {base_time:.3f}s")
        print(f"微调模型推理时间: {finetuned_time:.3f}s")
        print(f"速度比较: 微调模型 {'🚀' if finetuned_time < base_time else '🐌'} ({finetuned_time/base_time:.2f}x)")
        
        print("\n🎉 快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
